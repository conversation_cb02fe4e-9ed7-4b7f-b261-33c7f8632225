# Core FastAPI and web framework dependencies
fastapi==0.116.1
uvicorn[standard]==0.35.0
python-dotenv==1.1.1
httpx==0.28.1
pydantic==2.11.7

# AI and ML dependencies
openai==1.58.1
langchain==0.3.12
langchain-openai==0.2.14
langchain-community==0.3.12
sentence-transformers==3.3.1
transformers==4.35.0
torch==2.0.1

# Vector database and multimodal RAG
chromadb==0.5.23
faiss-cpu==1.9.0
pinecone-client==5.0.1
langchain==0.3.12
langchain-community==0.3.12
langchain-chroma==0.1.4
unstructured==0.18.3
pillow==10.0.0
opencv-python==*********
pytesseract==0.3.13
easyocr==1.7.2
sentence-transformers==3.3.1
clip-by-openai==1.0

# Document processing
pypdf2==3.0.1
python-docx==1.1.2
python-pptx==0.6.23
beautifulsoup4==4.12.3
markdown==3.7
pandas==2.2.3
openpyxl==3.1.5
xlrd==2.0.1
pymupdf==1.25.2
pdfplumber==0.11.4
tabula-py==2.9.3

# File monitoring
watchdog==6.0.0

# YouTube API
google-api-python-client==2.156.0
youtube-transcript-api==0.6.2

# Audio processing for STT/TTS with Pipecat
pipecat-ai==0.0.45
speechrecognition==3.12.0
pydub==0.25.1
gtts==2.5.4
azure-cognitiveservices-speech==1.41.1
websockets==12.0
aiofiles==24.1.0

# Database and caching
sqlalchemy==2.0.36
redis==5.2.1
psycopg2-binary==2.9.10

# Monitoring and logging
loguru==0.7.3
prometheus-client==0.21.1
sentry-sdk[fastapi]==2.19.2

# Security and authentication
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.20

# Development and testing
pytest==8.3.4
pytest-asyncio==0.24.0
black==24.10.0
flake8==7.1.1
