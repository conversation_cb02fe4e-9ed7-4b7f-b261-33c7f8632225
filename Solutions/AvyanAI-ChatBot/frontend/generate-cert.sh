#!/bin/bash

# Generate self-signed certificate for local development
echo "Generating self-signed certificate for localhost..."

# Create certificates directory
mkdir -p certs

# Generate private key
openssl genrsa -out certs/localhost.key 2048

# Generate certificate signing request
openssl req -new -key certs/localhost.key -out certs/localhost.csr -subj "/C=US/ST=CA/L=San Francisco/O=Avyan AI/OU=Development/CN=localhost"

# Generate self-signed certificate
openssl x509 -req -in certs/localhost.csr -signkey certs/localhost.key -out certs/localhost.crt -days 365 -extensions v3_req -extfile <(
cat <<EOF
[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = 127.0.0.1
IP.1 = 127.0.0.1
IP.2 = ::1
EOF
)

echo "Certificate generated successfully!"
echo "Files created:"
echo "  - certs/localhost.key (private key)"
echo "  - certs/localhost.crt (certificate)"
echo ""
echo "To trust the certificate in Safari:"
echo "1. Double-click certs/localhost.crt to add to Keychain"
echo "2. In Keychain Access, find 'localhost' certificate"
echo "3. Double-click it and set 'When using this certificate' to 'Always Trust'"
echo ""
echo "Then restart the development server with HTTPS=true"
