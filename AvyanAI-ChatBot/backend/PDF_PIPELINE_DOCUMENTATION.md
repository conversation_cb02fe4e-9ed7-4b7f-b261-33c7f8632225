# Automated PDF Processing Pipeline Documentation

## Overview

The Automated PDF Processing Pipeline is a comprehensive system that automatically processes PDF files and makes them retrievable by the Retrieval-Augmented Generation (RAG) system. The pipeline includes PDF ingestion, preprocessing, chunking, embedding, vector storage, and retrieval integration.

## Features

### ✅ PDF Ingestion
- **Automatic Detection**: Monitors the `data/documents/services/` directory for new, modified, or deleted PDF files
- **File Monitoring**: Real-time file system monitoring using watchdog
- **Debouncing**: Prevents duplicate processing during file operations
- **Hash-based Change Detection**: Only processes files that are new or have been modified

### ✅ Preprocessing & Parsing
- **Multi-method Text Extraction**:
  - **PDFPlumber**: Primary method for better table detection and extraction
  - **PyMuPDF**: Fallback method with OCR support for scanned documents
  - **OCR Support**: Tesseract OCR for image-based PDFs (optional)
- **Table Detection**: Automatically identifies and extracts tabular data
- **Metadata Preservation**: Captures page numbers, extraction methods, file properties

### ✅ Intelligent Chunking
- **Semantic Chunking**: Uses RecursiveCharacterTextSplitter for meaningful text segments
- **Overlapping Chunks**: Configurable overlap (default: 200 characters) for context preservation
- **Metadata Enhancement**: Each chunk includes:
  - Source file path and hash
  - Page numbers and ranges
  - Extraction method used
  - File properties (size, tables, images)
  - Processing timestamp

### ✅ Vector Embeddings
- **Sentence Transformers**: Uses `all-MiniLM-L6-v2` model for high-quality embeddings
- **Batch Processing**: Efficient embedding generation for multiple chunks
- **GPU Support**: Automatically uses MPS (Metal Performance Shaders) on Apple Silicon

### ✅ Vector Storage
- **ChromaDB**: Persistent vector database with cosine similarity search
- **Metadata Storage**: Rich metadata for filtering and source attribution
- **Efficient Indexing**: Optimized for fast retrieval and updates

### ✅ RAG Integration
- **Dual Search**: Searches both PDF pipeline and existing multimodal RAG system
- **Source Attribution**: Provides detailed source information for each result
- **Score-based Ranking**: Returns results ranked by semantic similarity

### ✅ Auto Refresh
- **File System Monitoring**: Automatic processing of new/modified files
- **Manual Refresh**: API endpoints for manual processing and refresh
- **Incremental Updates**: Only processes changed files to maintain efficiency

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PDF Files     │───▶│  File Monitor    │───▶│  PDF Processor  │
│ (services dir)  │    │  (Watchdog)      │    │  (Multi-method) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   RAG System    │◀───│  Vector Search   │◀───│  Text Chunker   │
│  Integration    │    │   (ChromaDB)     │    │  (Intelligent)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                ▲                        │
                                │                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Embeddings     │◀───│   Metadata      │
                       │ (SentenceTransf) │    │  Enhancement    │
                       └──────────────────┘    └─────────────────┘
```

## Installation & Setup

### Dependencies
```bash
pip install pdfplumber tabula-py watchdog PyMuPDF pytesseract sentence-transformers chromadb langchain langchain-community
```

### Optional OCR Setup
For OCR support, install Tesseract:
```bash
# macOS
brew install tesseract

# Ubuntu/Debian
sudo apt-get install tesseract-ocr

# Windows
# Download from: https://github.com/UB-Mannheim/tesseract/wiki
```

## Usage

### 1. Standalone Processing
```bash
# Process all PDFs
python initialize_pipeline.py process

# Test search functionality
python initialize_pipeline.py test

# Get processing statistics
python initialize_pipeline.py stats
```

### 2. API Integration
The pipeline is automatically integrated with the FastAPI application:

```python
# Start the server (pipeline auto-initializes)
python main.py
```

### 3. API Endpoints

#### Pipeline Status
```bash
GET /pipeline/status
```

#### Processing Statistics
```bash
GET /pipeline/stats
```

#### Search Documents
```bash
POST /pipeline/search
{
  "query": "Avyan AI services",
  "top_k": 5
}
```

#### Process All Files
```bash
POST /pipeline/process-all
```

#### File Monitoring Control
```bash
POST /pipeline/start-monitoring
POST /pipeline/stop-monitoring
```

#### Full Refresh
```bash
POST /pipeline/refresh-all
```

## Configuration

### Default Settings
- **Data Folder**: `data/`
- **Services Folder**: `data/documents/services/`
- **Embedding Model**: `all-MiniLM-L6-v2`
- **Chunk Size**: 1000 characters
- **Chunk Overlap**: 200 characters
- **Vector Database**: ChromaDB with cosine similarity

### Customization
```python
# Create custom pipeline
pipeline = PDFPipelineManager(
    data_folder="custom/data",
    services_folder="custom/pdfs",
    embedding_model="custom-model"
)

# Custom chunking
chunker = IntelligentChunker(
    chunk_size=1500,
    chunk_overlap=300
)
```

## Performance Metrics

### Current Statistics
- **Files Processed**: 9 PDF files
- **Total Chunks**: 77 text chunks
- **Extraction Success**: 100% (all files processed successfully)
- **Table Detection**: 8/9 files contain tables
- **Processing Speed**: ~2-3 seconds per file
- **Search Response**: <100ms for similarity search

### Extraction Methods Used
- **PDFPlumber**: 9/9 files (100%)
- **PyMuPDF**: 0/9 files (fallback not needed)
- **OCR**: 0/9 files (no scanned documents)

## Monitoring & Maintenance

### Health Checks
```bash
GET /pipeline/health
```

### Log Monitoring
The pipeline provides comprehensive logging:
- File processing status
- Extraction method selection
- Chunk creation and indexing
- Search performance
- Error handling and recovery

### Troubleshooting

#### Common Issues
1. **OCR Not Available**: Install Tesseract for scanned PDF support
2. **File Permission Errors**: Ensure read access to PDF directory
3. **Memory Issues**: Reduce chunk size for large documents
4. **Slow Processing**: Check GPU availability for embeddings

#### Performance Optimization
- Use GPU acceleration when available
- Adjust chunk size based on document types
- Monitor ChromaDB performance
- Regular cleanup of old embeddings

## Integration Examples

### Chat Integration
The pipeline is automatically integrated with the chat system:

```python
# Query example
response = await query_ai_model("What services does Avyan AI offer?")
# Returns: Combined results from PDF pipeline + existing RAG system
```

### Custom Search
```python
# Direct pipeline search
results = await pipeline.search("machine learning", top_k=5)
for result in results:
    print(f"Score: {result['score']}")
    print(f"Source: {result['metadata']['source']}")
    print(f"Content: {result['content'][:100]}...")
```

## Future Enhancements

### Planned Features
- [ ] Support for additional document formats (DOCX, PPTX)
- [ ] Advanced table extraction and processing
- [ ] Multi-language document support
- [ ] Document classification and tagging
- [ ] Advanced metadata extraction (authors, dates, topics)
- [ ] Integration with external document sources
- [ ] Automated document quality assessment
- [ ] Custom embedding model fine-tuning

### Scalability Improvements
- [ ] Distributed processing for large document sets
- [ ] Cloud storage integration (S3, GCS, Azure)
- [ ] Horizontal scaling with multiple workers
- [ ] Advanced caching strategies
- [ ] Real-time processing pipelines

## Security Considerations

- **File Access Control**: Ensure proper permissions on document directories
- **Data Privacy**: Consider encryption for sensitive documents
- **API Security**: Implement authentication for production deployments
- **Audit Logging**: Track document access and processing activities

## Support

For issues, questions, or contributions:
- **Email**: <EMAIL>
- **Website**: https://avyan.ai/
- **Documentation**: See inline code comments and API docs at `/docs`

---

**Build Smarter. Move Faster.**  
*Practical AI That Works, Not Just Impresses*
