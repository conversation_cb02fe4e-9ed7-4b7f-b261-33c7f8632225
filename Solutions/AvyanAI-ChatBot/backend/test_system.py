#!/usr/bin/env python3
"""
Test script for Avyan AI Chatbot System
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    response = requests.get(f"{BASE_URL}/health")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Health check passed: {data}")
        return True
    else:
        print(f"❌ Health check failed: {response.status_code}")
        return False

def test_chat():
    """Test chat endpoint"""
    print("\n💬 Testing chat endpoint...")
    
    test_messages = [
        "What is Avyan AI?",
        "Tell me about your services",
        "Who is on the Avyan AI team?",
        "What technologies do you use?"
    ]
    
    for message in test_messages:
        print(f"\n📤 Sending: {message}")
        
        payload = {
            "message": message,
            "context": [],
            "session_id": None,
            "use_voice": False
        }
        
        response = requests.post(f"{BASE_URL}/api/chat", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Response: {data['response'][:100]}...")
            print(f"📊 Sources found: {len(data['sources'])}")
        else:
            print(f"❌ Chat failed: {response.status_code}")
            return False
    
    return True

def test_tts():
    """Test text-to-speech endpoint"""
    print("\n🔊 Testing text-to-speech...")
    
    response = requests.post(f"{BASE_URL}/api/speak?text=Hello from Avyan AI&lang=en")
    
    if response.status_code == 200:
        print(f"✅ TTS working: Generated {len(response.content)} bytes of audio")
        return True
    else:
        print(f"❌ TTS failed: {response.status_code}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Avyan AI Chatbot System Tests\n")
    
    tests = [
        ("Health Check", test_health),
        ("Chat Functionality", test_chat),
        ("Text-to-Speech", test_tts)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print('='*50)
    
    if passed == total:
        print("🎉 All tests passed! System is working perfectly!")
        return True
    else:
        print("⚠️  Some tests failed. Check the logs above.")
        return False

if __name__ == "__main__":
    main()
