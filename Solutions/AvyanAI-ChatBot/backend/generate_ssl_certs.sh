#!/bin/bash

# Generate SSL Certificates for HTTPS Development
# This script creates self-signed certificates for local development

echo "🔐 Generating SSL certificates for HTTPS development..."

# Create certs directory if it doesn't exist
mkdir -p ../frontend/certs

# Generate private key and certificate
openssl req -x509 -newkey rsa:4096 \
    -keyout ../frontend/certs/localhost.key \
    -out ../frontend/certs/localhost.crt \
    -days 365 -nodes \
    -subj "/C=IN/ST=Maharashtra/L=Mumbai/O=Avyan AI/OU=Development/CN=localhost/emailAddress=<EMAIL>"

# Set appropriate permissions
chmod 600 ../frontend/certs/localhost.key
chmod 644 ../frontend/certs/localhost.crt

echo "✅ SSL certificates generated successfully!"
echo "📁 Certificates location: ../frontend/certs/"
echo "🔑 Private key: localhost.key"
echo "📜 Certificate: localhost.crt"
echo ""
echo "🚀 You can now run the server with HTTPS support:"
echo "   python main.py"
echo ""
echo "🌐 Access your application at:"
echo "   https://localhost:8000"
echo ""
echo "⚠️  Note: These are self-signed certificates for development only."
echo "   For production, use certificates from a trusted CA."
