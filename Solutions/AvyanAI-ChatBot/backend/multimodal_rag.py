"""
Multimodal RAG Implementation for Avyan AI Chatbot
Handles PDFs, PPTs, Excel sheets, and YouTube transcripts
"""

import os
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json
import hashlib
import io

# Document processing imports
import pandas as pd
import PyPDF2
from docx import Document
from pptx import Presentation
import fitz  # PyMuPDF
from PIL import Image
import pytesseract

# Vector database and embeddings
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
import numpy as np

# Langchain imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document as LangchainDocument
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import SentenceTransformerEmbeddings

logger = logging.getLogger(__name__)

class MultimodalRAG:
    def __init__(self, data_folder: str = "data", embedding_model: str = "all-MiniLM-L6-v2"):
        self.data_folder = Path(data_folder)
        self.embedding_model_name = embedding_model
        self.embedding_model = SentenceTransformer(embedding_model)
        
        # Initialize ChromaDB
        self.chroma_client = chromadb.PersistentClient(
            path=str(self.data_folder / "chroma_db"),
            settings=Settings(anonymized_telemetry=False)
        )
        
        # Create collections for different document types
        self.collections = {
            "documents": self._get_or_create_collection("documents"),
            "presentations": self._get_or_create_collection("presentations"),
            "spreadsheets": self._get_or_create_collection("spreadsheets"),
            "youtube_transcripts": self._get_or_create_collection("youtube_transcripts"),
            "images": self._get_or_create_collection("images")
        }
        
        # Text splitter for chunking
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        
        # Processed files tracking
        self.processed_files_path = self.data_folder / "processed_files.json"
        self.processed_files = self._load_processed_files()
        
        logger.info(f"MultimodalRAG initialized with data folder: {data_folder}")

    def _get_or_create_collection(self, name: str):
        """Get or create a ChromaDB collection"""
        try:
            return self.chroma_client.get_collection(name)
        except:
            return self.chroma_client.create_collection(
                name=name,
                metadata={"hnsw:space": "cosine"}
            )

    def _load_processed_files(self) -> Dict[str, str]:
        """Load the list of processed files with their hashes"""
        if self.processed_files_path.exists():
            with open(self.processed_files_path, 'r') as f:
                return json.load(f)
        return {}

    def _save_processed_files(self):
        """Save the list of processed files"""
        with open(self.processed_files_path, 'w') as f:
            json.dump(self.processed_files, f, indent=2)

    def _get_file_hash(self, file_path: Path) -> str:
        """Get MD5 hash of a file"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def _should_process_file(self, file_path: Path) -> bool:
        """Check if file should be processed (new or modified)"""
        file_str = str(file_path)
        current_hash = self._get_file_hash(file_path)
        
        if file_str not in self.processed_files:
            return True
        
        return self.processed_files[file_str] != current_hash

    async def process_pdf(self, file_path: Path) -> List[LangchainDocument]:
        """Process PDF files and extract text"""
        documents = []
        
        try:
            # Method 1: PyPDF2 for text extraction
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    if page_text.strip():
                        text += f"\n--- Page {page_num + 1} ---\n{page_text}"
            
            # Method 2: PyMuPDF for better text extraction and images
            if not text.strip():
                doc = fitz.open(file_path)
                text = ""
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    page_text = page.get_text()
                    if page_text.strip():
                        text += f"\n--- Page {page_num + 1} ---\n{page_text}"
                    
                    # Extract images and perform OCR
                    image_list = page.get_images()
                    for img_index, img in enumerate(image_list):
                        try:
                            xref = img[0]
                            pix = fitz.Pixmap(doc, xref)
                            if pix.n - pix.alpha < 4:  # GRAY or RGB
                                img_data = pix.tobytes("png")
                                img_pil = Image.open(io.BytesIO(img_data))
                                ocr_text = pytesseract.image_to_string(img_pil)
                                if ocr_text.strip():
                                    text += f"\n--- Image {img_index + 1} OCR ---\n{ocr_text}"
                            pix = None
                        except Exception as e:
                            logger.warning(f"Error processing image in PDF {file_path}: {e}")
                doc.close()
            
            if text.strip():
                # Split text into chunks
                chunks = self.text_splitter.split_text(text)
                for i, chunk in enumerate(chunks):
                    documents.append(LangchainDocument(
                        page_content=chunk,
                        metadata={
                            "source": str(file_path),
                            "type": "pdf",
                            "chunk_id": i,
                            "total_chunks": len(chunks)
                        }
                    ))
            
        except Exception as e:
            logger.error(f"Error processing PDF {file_path}: {e}")
        
        return documents

    async def process_pptx(self, file_path: Path) -> List[LangchainDocument]:
        """Process PowerPoint files and extract text and images"""
        documents = []
        
        try:
            prs = Presentation(file_path)
            full_text = ""
            
            for slide_num, slide in enumerate(prs.slides):
                slide_text = f"\n--- Slide {slide_num + 1} ---\n"
                
                # Extract text from shapes
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text += shape.text + "\n"
                
                # Extract text from tables
                for shape in slide.shapes:
                    if shape.has_table:
                        table = shape.table
                        for row in table.rows:
                            row_text = " | ".join([cell.text for cell in row.cells])
                            slide_text += row_text + "\n"
                
                full_text += slide_text
            
            if full_text.strip():
                # Split text into chunks
                chunks = self.text_splitter.split_text(full_text)
                for i, chunk in enumerate(chunks):
                    documents.append(LangchainDocument(
                        page_content=chunk,
                        metadata={
                            "source": str(file_path),
                            "type": "pptx",
                            "chunk_id": i,
                            "total_chunks": len(chunks)
                        }
                    ))
            
        except Exception as e:
            logger.error(f"Error processing PPTX {file_path}: {e}")
        
        return documents

    async def process_excel(self, file_path: Path) -> List[LangchainDocument]:
        """Process Excel files and extract structured data"""
        documents = []
        
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(file_path)
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # Convert DataFrame to text representation
                sheet_text = f"Sheet: {sheet_name}\n"
                sheet_text += f"Columns: {', '.join(df.columns.tolist())}\n\n"
                
                # Add data rows
                for index, row in df.iterrows():
                    row_text = " | ".join([f"{col}: {val}" for col, val in row.items() if pd.notna(val)])
                    sheet_text += f"Row {index + 1}: {row_text}\n"
                
                # Create document for each sheet
                documents.append(LangchainDocument(
                    page_content=sheet_text,
                    metadata={
                        "source": str(file_path),
                        "type": "excel",
                        "sheet_name": sheet_name,
                        "rows": len(df),
                        "columns": len(df.columns)
                    }
                ))
            
        except Exception as e:
            logger.error(f"Error processing Excel {file_path}: {e}")
        
        return documents

    async def process_youtube_transcript(self, file_path: Path) -> List[LangchainDocument]:
        """Process YouTube transcript files"""
        documents = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Try to parse as JSON (if structured transcript)
            try:
                transcript_data = json.loads(content)
                if isinstance(transcript_data, list):
                    # Structured transcript with timestamps
                    text = "\n".join([item.get('text', '') for item in transcript_data])
                else:
                    text = content
            except json.JSONDecodeError:
                # Plain text transcript
                text = content
            
            if text.strip():
                # Split text into chunks
                chunks = self.text_splitter.split_text(text)
                for i, chunk in enumerate(chunks):
                    documents.append(LangchainDocument(
                        page_content=chunk,
                        metadata={
                            "source": str(file_path),
                            "type": "youtube_transcript",
                            "chunk_id": i,
                            "total_chunks": len(chunks)
                        }
                    ))
            
        except Exception as e:
            logger.error(f"Error processing YouTube transcript {file_path}: {e}")
        
        return documents

    async def index_documents(self, documents: List[LangchainDocument], collection_name: str):
        """Index documents in ChromaDB"""
        if not documents:
            return

        try:
            collection = self.collections[collection_name]

            # Prepare data for ChromaDB
            texts = [doc.page_content for doc in documents]
            metadatas = [doc.metadata for doc in documents]
            ids = [f"{doc.metadata['source']}_{doc.metadata.get('chunk_id', 0)}" for doc in documents]

            # Generate embeddings
            embeddings = self.embedding_model.encode(texts).tolist()

            # Add to collection
            collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )

            logger.info(f"Indexed {len(documents)} documents in {collection_name}")

        except Exception as e:
            logger.error(f"Error indexing documents in {collection_name}: {e}")

    async def process_all_files(self):
        """Process all files in the data folder"""
        logger.info("Starting to process all files in data folder...")

        # Define file processors
        processors = {
            '.pdf': (self.process_pdf, 'documents'),
            '.pptx': (self.process_pptx, 'presentations'),
            '.ppt': (self.process_pptx, 'presentations'),
            '.xlsx': (self.process_excel, 'spreadsheets'),
            '.xls': (self.process_excel, 'spreadsheets'),
            '.txt': (self.process_youtube_transcript, 'youtube_transcripts'),
            '.json': (self.process_youtube_transcript, 'youtube_transcripts'),
        }

        # Process each file type
        for file_path in self.data_folder.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in processors:

                # Skip if already processed and unchanged
                if not self._should_process_file(file_path):
                    logger.info(f"Skipping {file_path} (already processed)")
                    continue

                processor, collection_name = processors[file_path.suffix.lower()]

                try:
                    logger.info(f"Processing {file_path}...")
                    documents = await processor(file_path)

                    if documents:
                        await self.index_documents(documents, collection_name)

                        # Mark as processed
                        self.processed_files[str(file_path)] = self._get_file_hash(file_path)
                        self._save_processed_files()

                        logger.info(f"Successfully processed {file_path}")
                    else:
                        logger.warning(f"No content extracted from {file_path}")

                except Exception as e:
                    logger.error(f"Error processing {file_path}: {e}")

    async def search(self, query: str, top_k: int = 5, collection_names: List[str] = None) -> List[Dict[str, Any]]:
        """Search across all collections or specific ones"""
        if collection_names is None:
            collection_names = list(self.collections.keys())

        all_results = []

        try:
            # Generate query embedding
            query_embedding = self.embedding_model.encode([query]).tolist()[0]

            # Search in each specified collection
            for collection_name in collection_names:
                if collection_name not in self.collections:
                    continue

                collection = self.collections[collection_name]

                try:
                    results = collection.query(
                        query_embeddings=[query_embedding],
                        n_results=min(top_k, 10),  # Limit per collection
                        include=['documents', 'metadatas', 'distances']
                    )

                    # Process results
                    for i in range(len(results['documents'][0])):
                        all_results.append({
                            'content': results['documents'][0][i],
                            'metadata': results['metadatas'][0][i],
                            'score': 1 - results['distances'][0][i],  # Convert distance to similarity
                            'collection': collection_name
                        })

                except Exception as e:
                    logger.error(f"Error searching in collection {collection_name}: {e}")

            # Sort by score and return top results
            all_results.sort(key=lambda x: x['score'], reverse=True)
            return all_results[:top_k]

        except Exception as e:
            logger.error(f"Error in search: {e}")
            return []

    async def get_context_for_query(self, query: str, max_context_length: int = 3000) -> Tuple[str, List[Dict]]:
        """Get relevant context for a query with source information"""
        search_results = await self.search(query, top_k=8)

        context_parts = []
        sources = []
        current_length = 0

        for result in search_results:
            content = result['content']
            metadata = result['metadata']

            # Add source information
            source_info = {
                'source': metadata.get('source', 'Unknown'),
                'type': metadata.get('type', 'Unknown'),
                'score': result['score']
            }

            # Add sheet name for Excel files
            if metadata.get('sheet_name'):
                source_info['sheet_name'] = metadata['sheet_name']

            # Check if adding this content would exceed max length
            if current_length + len(content) > max_context_length:
                break

            context_parts.append(f"Source: {source_info['source']}\n{content}")
            sources.append(source_info)
            current_length += len(content)

        context = "\n\n---\n\n".join(context_parts)
        return context, sources

    def get_collection_stats(self) -> Dict[str, int]:
        """Get statistics about indexed documents"""
        stats = {}
        for name, collection in self.collections.items():
            try:
                count = collection.count()
                stats[name] = count
            except:
                stats[name] = 0
        return stats

    async def clear_collection(self, collection_name: str):
        """Clear a specific collection"""
        if collection_name in self.collections:
            try:
                self.chroma_client.delete_collection(collection_name)
                self.collections[collection_name] = self._get_or_create_collection(collection_name)
                logger.info(f"Cleared collection: {collection_name}")
            except Exception as e:
                logger.error(f"Error clearing collection {collection_name}: {e}")

    async def clear_all_collections(self):
        """Clear all collections and reset processed files"""
        for collection_name in list(self.collections.keys()):
            await self.clear_collection(collection_name)

        # Reset processed files
        self.processed_files = {}
        self._save_processed_files()
        logger.info("Cleared all collections and reset processed files")
