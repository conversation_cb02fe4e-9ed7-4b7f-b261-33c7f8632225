#!/usr/bin/env python3
"""
Initialize and run the PDF processing pipeline
This script can be run standalone to process all PDFs or as part of the main application
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the backend directory to the Python path
sys.path.append(str(Path(__file__).parent))

from pdf_pipeline import create_pdf_pipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def initialize_and_process():
    """Initialize pipeline and process all existing PDFs"""
    try:
        logger.info("Initializing PDF processing pipeline...")
        
        # Create pipeline manager
        pipeline = await create_pdf_pipeline()
        
        # Check if services folder exists and has PDFs
        services_folder = Path("data/documents/services")
        if not services_folder.exists():
            logger.error(f"Services folder not found: {services_folder}")
            return False
        
        pdf_files = list(services_folder.glob("*.pdf"))
        logger.info(f"Found {len(pdf_files)} PDF files in services folder")
        
        if not pdf_files:
            logger.warning("No PDF files found to process")
            return True
        
        # Process all PDFs
        logger.info("Starting to process all PDF files...")
        results = await pipeline.process_all_pdfs()
        
        # Report results
        successful = sum(1 for success in results.values() if success)
        failed = len(results) - successful
        
        logger.info(f"Processing completed: {successful} successful, {failed} failed")
        
        # Show processing stats
        stats = pipeline.get_processing_stats()
        logger.info(f"Processing statistics: {stats}")
        
        # Start file monitoring
        logger.info("Starting file monitoring...")
        pipeline.start_monitoring()
        
        return True
        
    except Exception as e:
        logger.error(f"Error during pipeline initialization: {e}")
        return False

async def test_search():
    """Test the search functionality"""
    try:
        logger.info("Testing search functionality...")
        
        pipeline = await create_pdf_pipeline()
        
        # Test queries
        test_queries = [
            "Avyan AI services",
            "artificial intelligence solutions",
            "machine learning",
            "data analytics",
            "chatbot development"
        ]
        
        for query in test_queries:
            logger.info(f"Testing query: '{query}'")
            results = await pipeline.search(query, top_k=3)
            
            if results:
                logger.info(f"Found {len(results)} results for '{query}'")
                for i, result in enumerate(results[:2]):  # Show top 2 results
                    logger.info(f"  Result {i+1}: Score {result['score']:.3f}, Source: {result['metadata'].get('source', 'Unknown')}")
            else:
                logger.warning(f"No results found for '{query}'")
        
        return True
        
    except Exception as e:
        logger.error(f"Error during search testing: {e}")
        return False

async def main():
    """Main function"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "process":
            logger.info("Running PDF processing...")
            success = await initialize_and_process()
            sys.exit(0 if success else 1)
            
        elif command == "test":
            logger.info("Running search tests...")
            success = await test_search()
            sys.exit(0 if success else 1)
            
        elif command == "stats":
            logger.info("Getting processing statistics...")
            try:
                pipeline = await create_pdf_pipeline()
                stats = pipeline.get_processing_stats()
                print("\n=== PDF Processing Statistics ===")
                print(f"Total files processed: {stats['total_files_processed']}")
                print(f"Total chunks created: {stats['total_chunks_created']}")
                print(f"Total chunks indexed: {stats['total_chunks_indexed']}")
                print(f"Files with tables: {stats['files_with_tables']}")
                print(f"Files with images: {stats['files_with_images']}")
                print(f"Extraction methods: {stats['extraction_methods']}")
                sys.exit(0)
            except Exception as e:
                logger.error(f"Error getting stats: {e}")
                sys.exit(1)
                
        else:
            print("Usage: python initialize_pipeline.py [process|test|stats]")
            print("  process - Initialize pipeline and process all PDFs")
            print("  test    - Test search functionality")
            print("  stats   - Show processing statistics")
            sys.exit(1)
    else:
        # Default: process all PDFs
        logger.info("No command specified, running default processing...")
        success = await initialize_and_process()
        
        if success:
            logger.info("Pipeline initialization completed successfully!")
            logger.info("File monitoring is now active. The pipeline will automatically process new PDFs.")
            
            # Keep the script running to maintain file monitoring
            try:
                while True:
                    await asyncio.sleep(60)  # Check every minute
                    logger.debug("Pipeline monitoring active...")
            except KeyboardInterrupt:
                logger.info("Shutting down pipeline...")
        else:
            logger.error("Pipeline initialization failed!")
            sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
