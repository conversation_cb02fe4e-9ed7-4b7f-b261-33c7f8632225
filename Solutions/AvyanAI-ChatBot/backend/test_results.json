{"status": "completed", "passed": 5, "total": 5, "success_rate": 1.0, "test_results": {"pdf_detection": {"status": "passed", "files_found": 9, "files": ["data/documents/services/2_Energy-Consumption-Predictive Forecasting.pdf", "data/documents/services/6_GenAI-Use-Cases-Across-Corporate-Functions.pdf", "data/documents/services/5_Telecom-Tower-Use-Cases-Leveraging-AI-for-Efficiency-and-Growth.pdf", "data/documents/services/4_AI-Agents-for-SDLC-Framework.pdf", "data/documents/services/3_Intelligent-RAG-Chatbot-for-Customer-Support.pdf", "data/documents/services/8_Gen-AI-Vision-and-Strategy-Execution-Framework.pdf", "data/documents/services/Smart-City-Solutions-AI-Innovations-Transforming-Urban-Life.pdf", "data/documents/services/7_GenAI-Use-Cases-in-Healthcare.pdf", "data/documents/services/1_Avyan AI Services Catalogue.pdf"]}, "text_extraction": {"status": "passed", "file_tested": "data/documents/services/2_Energy-Consumption-Predictive Forecasting.pdf", "text_length": 5441, "metadata": {"total_pages": 18, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false}}, "chunking": {"status": "passed", "chunks_created": 9, "sample_chunk_length": 14}, "pipeline_processing": {"status": "passed", "file_tested": "data/documents/services/2_Energy-Consumption-Predictive Forecasting.pdf", "stats": {"total_files_processed": 1, "total_chunks_created": 9, "total_chunks_indexed": 9, "files_with_tables": 1, "files_with_images": 0, "extraction_methods": {"pdfplumber": 1, "pymupdf": 0, "pymupdf+ocr": 0}}}, "search_functionality": {"status": "passed", "search_results": {"Avyan AI": {"results_count": 3, "top_score": 0.18532854318618774}, "artificial intelligence": {"results_count": 3, "top_score": 0.18342924118041992}, "machine learning": {"results_count": 3, "top_score": 0.3334167003631592}, "services": {"results_count": 3, "top_score": 0.13127648830413818}}}}}