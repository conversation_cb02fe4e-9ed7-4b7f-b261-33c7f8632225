"""
API endpoints for PDF processing pipeline management
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from pathlib import Path

from pdf_pipeline import PDFPipelineManager

logger = logging.getLogger(__name__)

# Global pipeline manager instance
pipeline_manager: Optional[PDFPipelineManager] = None

# Pydantic models for API requests/responses
class ProcessingStatus(BaseModel):
    status: str
    message: str
    file_path: Optional[str] = None
    success: Optional[bool] = None

class ProcessingStats(BaseModel):
    total_files_processed: int
    total_chunks_created: int
    total_chunks_indexed: int
    files_with_tables: int
    files_with_images: int
    extraction_methods: Dict[str, int]

class SearchRequest(BaseModel):
    query: str
    top_k: int = 5

class SearchResult(BaseModel):
    content: str
    metadata: Dict[str, Any]
    score: float
    collection: str

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total_results: int
    query: str

class RefreshResponse(BaseModel):
    processing_results: Dict[str, bool]
    stats: ProcessingStats

# Create API router
router = APIRouter(prefix="/pipeline", tags=["PDF Pipeline"])

async def get_pipeline_manager() -> PDFPipelineManager:
    """Get or create pipeline manager instance"""
    global pipeline_manager
    if pipeline_manager is None:
        try:
            from pdf_pipeline import create_pdf_pipeline
            pipeline_manager = await create_pdf_pipeline()
            logger.info("Pipeline manager initialized")
        except Exception as e:
            logger.error(f"Failed to initialize pipeline manager: {e}")
            raise HTTPException(status_code=500, detail="Failed to initialize pipeline manager")
    return pipeline_manager

@router.get("/status", response_model=ProcessingStatus)
async def get_pipeline_status():
    """Get current pipeline status"""
    try:
        manager = await get_pipeline_manager()
        stats = manager.get_processing_stats()
        
        return ProcessingStatus(
            status="active",
            message=f"Pipeline active. {stats['total_files_processed']} files processed, {stats['total_chunks_indexed']} chunks indexed."
        )
    except Exception as e:
        logger.error(f"Error getting pipeline status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats", response_model=ProcessingStats)
async def get_processing_stats():
    """Get detailed processing statistics"""
    try:
        manager = await get_pipeline_manager()
        stats = manager.get_processing_stats()
        
        return ProcessingStats(**stats)
    except Exception as e:
        logger.error(f"Error getting processing stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/process-all")
async def process_all_pdfs(background_tasks: BackgroundTasks):
    """Process all PDF files in the services directory"""
    try:
        manager = await get_pipeline_manager()
        
        # Run processing in background
        background_tasks.add_task(manager.process_all_pdfs)
        
        return ProcessingStatus(
            status="started",
            message="Processing all PDF files started in background"
        )
    except Exception as e:
        logger.error(f"Error starting PDF processing: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/process-file")
async def process_single_file(file_path: str, background_tasks: BackgroundTasks):
    """Process a single PDF file"""
    try:
        manager = await get_pipeline_manager()
        
        # Validate file path
        pdf_path = Path(file_path)
        if not pdf_path.exists():
            raise HTTPException(status_code=404, detail="File not found")
        
        if not pdf_path.suffix.lower() == '.pdf':
            raise HTTPException(status_code=400, detail="File must be a PDF")
        
        # Run processing in background
        background_tasks.add_task(manager.process_single_file, pdf_path)
        
        return ProcessingStatus(
            status="started",
            message=f"Processing started for {file_path}",
            file_path=file_path
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/remove-file")
async def remove_file_from_index(file_path: str):
    """Remove a file from the vector index"""
    try:
        manager = await get_pipeline_manager()
        await manager.remove_document(file_path)
        
        return ProcessingStatus(
            status="success",
            message=f"File removed from index: {file_path}",
            file_path=file_path,
            success=True
        )
    except Exception as e:
        logger.error(f"Error removing file {file_path}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """Search for relevant document chunks"""
    try:
        manager = await get_pipeline_manager()
        results = await manager.search(request.query, request.top_k)
        
        search_results = [
            SearchResult(
                content=result['content'],
                metadata=result['metadata'],
                score=result['score'],
                collection=result['collection']
            )
            for result in results
        ]
        
        return SearchResponse(
            results=search_results,
            total_results=len(search_results),
            query=request.query
        )
    except Exception as e:
        logger.error(f"Error searching documents: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/start-monitoring")
async def start_file_monitoring():
    """Start automatic file monitoring"""
    try:
        manager = await get_pipeline_manager()
        manager.start_monitoring()
        
        return ProcessingStatus(
            status="success",
            message="File monitoring started"
        )
    except Exception as e:
        logger.error(f"Error starting file monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stop-monitoring")
async def stop_file_monitoring():
    """Stop automatic file monitoring"""
    try:
        manager = await get_pipeline_manager()
        manager.stop_monitoring()
        
        return ProcessingStatus(
            status="success",
            message="File monitoring stopped"
        )
    except Exception as e:
        logger.error(f"Error stopping file monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/refresh-all", response_model=RefreshResponse)
async def refresh_all_documents(background_tasks: BackgroundTasks):
    """Refresh all documents (reprocess everything)"""
    try:
        manager = await get_pipeline_manager()
        
        # Run refresh in background
        async def run_refresh():
            try:
                result = await manager.refresh_all()
                logger.info("Full refresh completed successfully")
                return result
            except Exception as e:
                logger.error(f"Error during full refresh: {e}")
                raise
        
        background_tasks.add_task(run_refresh)
        
        return ProcessingStatus(
            status="started",
            message="Full document refresh started in background"
        )
    except Exception as e:
        logger.error(f"Error starting document refresh: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/files")
async def list_processed_files():
    """List all processed files with metadata"""
    try:
        manager = await get_pipeline_manager()
        
        return {
            "processed_files": manager.processing_metadata,
            "total_files": len(manager.processing_metadata)
        }
    except Exception as e:
        logger.error(f"Error listing processed files: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for the pipeline"""
    try:
        manager = await get_pipeline_manager()
        stats = manager.get_processing_stats()
        
        return {
            "status": "healthy",
            "pipeline_active": True,
            "files_processed": stats["total_files_processed"],
            "chunks_indexed": stats["total_chunks_indexed"]
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "pipeline_active": False,
            "error": str(e)
        }
