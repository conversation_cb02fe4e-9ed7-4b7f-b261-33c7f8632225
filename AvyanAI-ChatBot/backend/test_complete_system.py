#!/usr/bin/env python3
"""
Complete System Test for Avyan AI Chatbot
Tests all features: PDF Pipeline, Multilingual Support, HTTPS, RAG System
"""

import asyncio
import json
import time
from typing import Dict, Any
import httpx

class AvyanAISystemTester:
    """Comprehensive system tester for Avyan AI Chatbot"""
    
    def __init__(self, base_url: str = "https://localhost:8000"):
        self.base_url = base_url
        self.session_id = f"test_session_{int(time.time())}"
        self.results = {}
    
    async def test_health_check(self) -> bool:
        """Test system health and feature availability"""
        print("🏥 Testing System Health...")
        try:
            async with httpx.AsyncClient(verify=False) as client:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200:
                    health_data = response.json()
                    print(f"✅ System Status: {health_data['status']}")
                    print(f"✅ RAG System: {health_data['rag_system']}")
                    print(f"✅ PDF Pipeline: {health_data['pdf_pipeline']}")
                    print(f"✅ Multilingual: {health_data['features']['multilingual']}")
                    print(f"✅ HTTPS: SSL certificates working")
                    print(f"✅ Supported Languages: {health_data['supported_languages']}")
                    
                    self.results['health_check'] = {
                        'status': 'passed',
                        'data': health_data
                    }
                    return True
                else:
                    print(f"❌ Health check failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Health check error: {e}")
            self.results['health_check'] = {'status': 'failed', 'error': str(e)}
            return False
    
    async def test_pdf_pipeline(self) -> bool:
        """Test PDF processing pipeline"""
        print("\n📄 Testing PDF Pipeline...")
        try:
            async with httpx.AsyncClient(verify=False) as client:
                # Test pipeline stats
                response = await client.get(f"{self.base_url}/pipeline/stats")
                
                if response.status_code == 200:
                    stats = response.json()
                    print(f"✅ Files Processed: {stats['total_files_processed']}")
                    print(f"✅ Chunks Created: {stats['total_chunks_created']}")
                    print(f"✅ Chunks Indexed: {stats['total_chunks_indexed']}")
                    print(f"✅ Files with Tables: {stats['files_with_tables']}")
                    
                    # Test search functionality
                    search_response = await client.post(
                        f"{self.base_url}/pipeline/search",
                        json={"query": "Avyan AI services", "top_k": 3}
                    )
                    
                    if search_response.status_code == 200:
                        search_results = search_response.json()
                        print(f"✅ Search Results: {search_results['total_results']} found")
                        
                        self.results['pdf_pipeline'] = {
                            'status': 'passed',
                            'stats': stats,
                            'search_results': search_results['total_results']
                        }
                        return True
                    else:
                        print(f"❌ Search failed: {search_response.status_code}")
                        return False
                else:
                    print(f"❌ Pipeline stats failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ PDF Pipeline error: {e}")
            self.results['pdf_pipeline'] = {'status': 'failed', 'error': str(e)}
            return False
    
    async def test_translation(self) -> bool:
        """Test multilingual translation"""
        print("\n🌐 Testing Translation Service...")
        try:
            async with httpx.AsyncClient(verify=False) as client:
                # Test English to Hindi
                en_to_hi = await client.post(
                    f"{self.base_url}/api/translate",
                    json={
                        "text": "Hello, how are you?",
                        "target_lang": "hi",
                        "source_lang": "en"
                    }
                )
                
                if en_to_hi.status_code == 200:
                    translation = en_to_hi.json()
                    print(f"✅ EN→HI: '{translation['original_text']}' → '{translation['translated_text']}'")
                    
                    # Test Hindi to English
                    hi_to_en = await client.post(
                        f"{self.base_url}/api/translate",
                        json={
                            "text": "नमस्ते, आप कैसे हैं?",
                            "target_lang": "en",
                            "source_lang": "hi"
                        }
                    )
                    
                    if hi_to_en.status_code == 200:
                        translation2 = hi_to_en.json()
                        print(f"✅ HI→EN: '{translation2['original_text']}' → '{translation2['translated_text']}'")
                        
                        self.results['translation'] = {
                            'status': 'passed',
                            'en_to_hi': translation,
                            'hi_to_en': translation2
                        }
                        return True
                    else:
                        print(f"❌ Hindi to English translation failed: {hi_to_en.status_code}")
                        return False
                else:
                    print(f"❌ English to Hindi translation failed: {en_to_hi.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Translation error: {e}")
            self.results['translation'] = {'status': 'failed', 'error': str(e)}
            return False
    
    async def test_multilingual_chat(self) -> bool:
        """Test multilingual chat functionality"""
        print("\n💬 Testing Multilingual Chat...")
        try:
            async with httpx.AsyncClient(verify=False, timeout=60.0) as client:
                # Test English chat
                en_response = await client.post(
                    f"{self.base_url}/api/chat",
                    json={
                        "message": "What services does Avyan AI offer?",
                        "session_id": self.session_id,
                        "language": "en"
                    }
                )
                
                if en_response.status_code == 200:
                    en_chat = en_response.json()
                    print(f"✅ English Chat: Response received ({len(en_chat['response'])} chars)")
                    print(f"✅ Sources Found: {len(en_chat['sources'])}")
                    
                    # Test Hindi chat
                    hi_response = await client.post(
                        f"{self.base_url}/api/chat",
                        json={
                            "message": "एवीआन एआई कौन सी सेवाएं प्रदान करता है?",
                            "session_id": self.session_id,
                            "language": "hi"
                        }
                    )
                    
                    if hi_response.status_code == 200:
                        hi_chat = hi_response.json()
                        print(f"✅ Hindi Chat: Response received ({len(hi_chat['response'])} chars)")
                        print(f"✅ Sources Found: {len(hi_chat['sources'])}")
                        
                        self.results['multilingual_chat'] = {
                            'status': 'passed',
                            'english_response_length': len(en_chat['response']),
                            'hindi_response_length': len(hi_chat['response']),
                            'sources_count': len(en_chat['sources'])
                        }
                        return True
                    else:
                        print(f"❌ Hindi chat failed: {hi_response.status_code}")
                        return False
                else:
                    print(f"❌ English chat failed: {en_response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Multilingual chat error: {e}")
            self.results['multilingual_chat'] = {'status': 'failed', 'error': str(e)}
            return False
    
    async def test_language_support(self) -> bool:
        """Test language support endpoints"""
        print("\n🗣️ Testing Language Support...")
        try:
            async with httpx.AsyncClient(verify=False) as client:
                response = await client.get(f"{self.base_url}/api/languages")
                
                if response.status_code == 200:
                    languages = response.json()
                    print(f"✅ Supported Languages: {list(languages['languages'].keys())}")
                    print(f"✅ Default Language: {languages['default']}")
                    
                    self.results['language_support'] = {
                        'status': 'passed',
                        'languages': languages
                    }
                    return True
                else:
                    print(f"❌ Language support failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Language support error: {e}")
            self.results['language_support'] = {'status': 'failed', 'error': str(e)}
            return False
    
    async def test_https_security(self) -> bool:
        """Test HTTPS security features"""
        print("\n🔒 Testing HTTPS Security...")
        try:
            # Test HTTPS connection
            async with httpx.AsyncClient(verify=False) as client:
                response = await client.get(f"{self.base_url}/health")
                
                if response.status_code == 200 and self.base_url.startswith("https"):
                    print("✅ HTTPS Connection: Working")
                    print("✅ SSL Certificates: Valid (self-signed)")
                    print("✅ Secure Communication: Enabled")
                    
                    self.results['https_security'] = {
                        'status': 'passed',
                        'https_enabled': True,
                        'ssl_working': True
                    }
                    return True
                else:
                    print("❌ HTTPS connection failed")
                    return False
                    
        except Exception as e:
            print(f"❌ HTTPS security error: {e}")
            self.results['https_security'] = {'status': 'failed', 'error': str(e)}
            return False
    
    async def run_complete_test(self) -> Dict[str, Any]:
        """Run all tests and return comprehensive results"""
        print("🚀 Starting Comprehensive Avyan AI System Test")
        print("=" * 60)
        
        tests = [
            ("System Health Check", self.test_health_check),
            ("PDF Processing Pipeline", self.test_pdf_pipeline),
            ("Translation Service", self.test_translation),
            ("Language Support", self.test_language_support),
            ("Multilingual Chat", self.test_multilingual_chat),
            ("HTTPS Security", self.test_https_security)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running: {test_name}")
            print("-" * 40)
            
            try:
                result = await test_func()
                if result:
                    print(f"✅ {test_name}: PASSED")
                    passed += 1
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
        
        # Final summary
        print("\n" + "=" * 60)
        print(f"🎯 TEST SUMMARY: {passed}/{total} tests passed")
        print(f"📊 Success Rate: {(passed/total)*100:.1f}%")
        print("=" * 60)
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! System is fully operational.")
        else:
            print("⚠️  Some tests failed. Check the logs above for details.")
        
        return {
            'summary': {
                'total_tests': total,
                'passed_tests': passed,
                'success_rate': (passed/total)*100,
                'status': 'all_passed' if passed == total else 'some_failed'
            },
            'detailed_results': self.results
        }

async def main():
    """Main test function"""
    print("🤖 Avyan AI Chatbot - Complete System Test")
    print("Build Smarter. Move Faster.")
    print("Practical AI That Works, Not Just Impresses")
    print()
    
    tester = AvyanAISystemTester()
    results = await tester.run_complete_test()
    
    # Save results to file
    with open('complete_system_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: complete_system_test_results.json")
    
    # Exit with appropriate code
    if results['summary']['status'] == 'all_passed':
        print("\n🚀 System is ready for production use!")
        exit(0)
    else:
        print("\n🔧 System needs attention before production use.")
        exit(1)

if __name__ == "__main__":
    asyncio.run(main())
