{"data/documents/services/2_Energy-Consumption-Predictive Forecasting.pdf": {"file_path": "data/documents/services/2_Energy-Consumption-Predictive Forecasting.pdf", "file_hash": "b7e121282ff0b54150adce7d18207acd", "processed_at": "2025-07-25 09:55:37.209905", "total_pages": 18, "total_chunks": 9, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false, "file_size": 964331}, "data/documents/services/6_GenAI-Use-Cases-Across-Corporate-Functions.pdf": {"file_path": "data/documents/services/6_GenAI-Use-Cases-Across-Corporate-Functions.pdf", "file_hash": "ea4b4887791b02110b7bef103be89c6e", "processed_at": "2025-07-25 09:55:56.321958", "total_pages": 11, "total_chunks": 13, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false, "file_size": 587414}, "data/documents/services/5_Telecom-Tower-Use-Cases-Leveraging-AI-for-Efficiency-and-Growth.pdf": {"file_path": "data/documents/services/5_Telecom-Tower-Use-Cases-Leveraging-AI-for-Efficiency-and-Growth.pdf", "file_hash": "2867be0b1551bef34de503b1e9bcc63e", "processed_at": "2025-07-25 09:55:56.774191", "total_pages": 12, "total_chunks": 7, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false, "file_size": 830820}, "data/documents/services/4_AI-Agents-for-SDLC-Framework.pdf": {"file_path": "data/documents/services/4_AI-Agents-for-SDLC-Framework.pdf", "file_hash": "c14c1661fbfd39a7d6bf6ecc3d139ac4", "processed_at": "2025-07-25 09:55:57.198408", "total_pages": 15, "total_chunks": 13, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false, "file_size": 1044033}, "data/documents/services/3_Intelligent-RAG-Chatbot-for-Customer-Support.pdf": {"file_path": "data/documents/services/3_Intelligent-RAG-Chatbot-for-Customer-Support.pdf", "file_hash": "2cac3a383a307d02f8d59dc28ec4a3ce", "processed_at": "2025-07-25 09:55:57.369715", "total_pages": 9, "total_chunks": 5, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false, "file_size": 475165}, "data/documents/services/8_Gen-AI-Vision-and-Strategy-Execution-Framework.pdf": {"file_path": "data/documents/services/8_Gen-AI-Vision-and-Strategy-Execution-Framework.pdf", "file_hash": "b6c5fc25e7467e8020846303c5bdf82b", "processed_at": "2025-07-25 09:55:57.784223", "total_pages": 9, "total_chunks": 8, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false, "file_size": 480270}, "data/documents/services/Smart-City-Solutions-AI-Innovations-Transforming-Urban-Life.pdf": {"file_path": "data/documents/services/Smart-City-Solutions-AI-Innovations-Transforming-Urban-Life.pdf", "file_hash": "fa5262f6d90fb21def9501d776adb80f", "processed_at": "2025-07-25 09:55:58.134636", "total_pages": 10, "total_chunks": 9, "extraction_method": "pdfplumber", "has_tables": false, "has_images": false, "file_size": 685656}, "data/documents/services/7_GenAI-Use-Cases-in-Healthcare.pdf": {"file_path": "data/documents/services/7_GenAI-Use-Cases-in-Healthcare.pdf", "file_hash": "d1841d5f42a5492774057803dcb13f31", "processed_at": "2025-07-25 09:55:58.361190", "total_pages": 12, "total_chunks": 9, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false, "file_size": 752295}, "data/documents/services/1_Avyan AI Services Catalogue.pdf": {"file_path": "data/documents/services/1_Avyan AI Services Catalogue.pdf", "file_hash": "af840e3c1619eed4fa488a126fa72b26", "processed_at": "2025-07-25 09:55:58.516448", "total_pages": 8, "total_chunks": 4, "extraction_method": "pdfplumber", "has_tables": true, "has_images": false, "file_size": 361696}}