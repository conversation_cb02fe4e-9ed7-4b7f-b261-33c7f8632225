#!/bin/bash

echo "🚀 Starting Avyan AI Chatbot..."
echo "=================================="

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ Error: Please run this script from the Avyan_Chatbot root directory"
    exit 1
fi

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "✅ Port $1 is already in use"
        return 0
    else
        echo "❌ Port $1 is not in use"
        return 1
    fi
}

# Check backend
echo "🔍 Checking backend (port 8000)..."
if check_port 8000; then
    echo "✅ Backend is already running"
else
    echo "🚀 Starting backend..."
    cd backend
    source venv/bin/activate
    uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
    BACKEND_PID=$!
    cd ..
    echo "✅ Backend started with PID: $BACKEND_PID"
    sleep 3
fi

# Check frontend
echo "🔍 Checking frontend (port 3000)..."
if check_port 3000; then
    echo "✅ Frontend is already running"
else
    echo "🚀 Starting frontend..."
    cd frontend
    npm start &
    FRONTEND_PID=$!
    cd ..
    echo "✅ Frontend started with PID: $FRONTEND_PID"
    sleep 5
fi

# Test the system
echo "🧪 Testing system..."
sleep 2

# Test backend health
echo "🔍 Testing backend health..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Backend health check passed"
else
    echo "❌ Backend health check failed"
fi

# Test frontend
echo "🔍 Testing frontend..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Frontend is accessible"
else
    echo "❌ Frontend is not accessible"
fi

echo ""
echo "🎉 Avyan AI Chatbot is ready!"
echo "=================================="
echo "🌐 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📊 API Health: http://localhost:8000/health"
echo ""
echo "💡 Open http://localhost:3000 in your browser to start chatting!"
echo ""
echo "🛑 To stop the servers, press Ctrl+C or run: pkill -f 'uvicorn\|react-scripts'"
