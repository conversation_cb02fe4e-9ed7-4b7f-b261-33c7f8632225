<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Error Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        button {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: scale(1.05);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .error {
            background: rgba(244, 67, 54, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
        .info {
            background: rgba(33, 150, 243, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Voice Recognition Error Handling Test</h1>
        
        <div class="info">
            <strong>ℹ️ Information:</strong><br>
            • This page tests voice recognition error handling<br>
            • Voice features require HTTPS or localhost<br>
            • Current protocol: <strong id="protocol"></strong><br>
            • Current hostname: <strong id="hostname"></strong>
        </div>

        <div id="error-message" class="error"></div>
        <div id="success-message" class="success"></div>

        <button onclick="testMicrophonePermission()">Test Microphone Permission</button>
        <button onclick="testSpeechRecognition()">Test Speech Recognition</button>
        <button onclick="clearMessages()">Clear Messages</button>

        <div id="status" style="margin-top: 20px; font-size: 14px;"></div>
    </div>

    <script>
        // Display current protocol and hostname
        document.getElementById('protocol').textContent = window.location.protocol;
        document.getElementById('hostname').textContent = window.location.hostname;

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 3000);
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        async function testMicrophonePermission() {
            updateStatus('Testing microphone permission...');
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                showSuccess('✅ Microphone permission granted successfully!');
                updateStatus('Microphone access: ✅ Allowed');
            } catch (error) {
                console.error('Microphone permission denied:', error);
                showError('🎤 Microphone permission denied. Please allow microphone access and try again.');
                updateStatus('Microphone access: ❌ Denied - ' + error.name);
            }
        }

        function testSpeechRecognition() {
            updateStatus('Testing speech recognition...');
            
            if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                showError('❌ Speech Recognition API not supported in this browser');
                updateStatus('Speech Recognition: ❌ Not supported');
                return;
            }

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const recognition = new SpeechRecognition();
            
            recognition.continuous = false;
            recognition.interimResults = false;
            recognition.lang = 'en-US';

            recognition.onstart = function() {
                updateStatus('Speech recognition started - speak now!');
                showSuccess('🎤 Listening... Speak now!');
            };

            recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                showSuccess('✅ Speech recognized: "' + transcript + '"');
                updateStatus('Last recognized: ' + transcript);
            };

            recognition.onerror = function(event) {
                console.error('Speech recognition error:', event.error);
                
                if (event.error === 'not-allowed' || event.error === 'service-not-allowed') {
                    showError('🎤 Microphone access denied. Please enable microphone permissions in your browser settings or use HTTPS for voice features.');
                    updateStatus('Speech Recognition: ❌ Permission denied');
                } else if (event.error === 'no-speech') {
                    showError('🔇 No speech detected. Please try again and speak clearly.');
                    updateStatus('Speech Recognition: ⚠️ No speech detected');
                } else if (event.error === 'network') {
                    showError('🌐 Network error. Please check your internet connection.');
                    updateStatus('Speech Recognition: ❌ Network error');
                } else {
                    showError('❌ Speech recognition error: ' + event.error);
                    updateStatus('Speech Recognition: ❌ Error - ' + event.error);
                }
            };

            recognition.onend = function() {
                updateStatus('Speech recognition ended');
            };

            try {
                recognition.start();
            } catch (error) {
                showError('❌ Failed to start speech recognition: ' + error.message);
                updateStatus('Speech Recognition: ❌ Failed to start');
            }
        }

        function clearMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
            document.getElementById('status').textContent = '';
        }

        // Auto-test on page load
        window.onload = function() {
            updateStatus('Page loaded - ready for testing');
            
            // Show protocol warning if not HTTPS or localhost
            if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
                showError('⚠️ Voice features may not work properly. This page is not served over HTTPS or localhost.');
            }
        };
    </script>
</body>
</html>