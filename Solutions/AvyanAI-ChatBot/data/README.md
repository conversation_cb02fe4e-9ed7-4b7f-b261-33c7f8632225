# Avyan AI Chatbot Data Directory

This directory contains the knowledge base for the Avyan AI voice-enabled chatbot.

## Directory Structure

```
data/
├── documents/          # Internal documentation files (PDFs, DOCX)
│   ├── products/       # Product documentation
│   ├── solutions/      # Solution documentation
│   ├── services/       # Service documentation
│   └── training/       # Training materials
├── presentations/      # PowerPoint presentations (PPTX, PPT)
│   ├── product_demos/  # Product demonstration slides
│   ├── training_slides/ # Training presentation materials
│   └── company_info/   # Company overview presentations
├── spreadsheets/       # Excel files with structured data (XLSX, XLS)
│   ├── links.xlsx      # All required links and resources
│   ├── products.xlsx   # Product information and specifications
│   └── contacts.xlsx   # Contact information and resources
├── youtube_transcripts/ # YouTube video transcripts (TXT, JSON)
│   ├── training_videos/ # Transcripts from training videos
│   ├── product_demos/  # Transcripts from product demonstrations
│   └── webinars/       # Transcripts from webinars and talks
├── chroma_db/          # Vector database storage (auto-generated)
├── processed_files.json # Tracking processed files (auto-generated)
└── README.md           # This file
```

## Supported File Types

### Documents
- **PDF files** (.pdf) - Product manuals, whitepapers, technical documentation
- **Word documents** (.docx) - Internal documentation, proposals
- **Text files** (.txt) - Plain text documentation
- **Markdown files** (.md) - Technical documentation
- **PowerPoint files** (.pptx) - Presentation materials

### Videos
- YouTube video metadata and transcripts from Avyan AI channel
- Training video content and descriptions

## Usage Instructions

1. **Adding Documents**: Place your documents in the appropriate subdirectory under `documents/`
2. **Processing**: The chatbot will automatically process and index documents when uploaded via the API
3. **Search**: Documents are searchable through the chatbot interface using natural language queries

## API Endpoints for Data Management

- `POST /api/docs/upload` - Upload new documents
- `GET /api/docs/search` - Search existing documents
- `GET /api/youtube` - Search YouTube videos
- `GET /api/health` - Check system status

## Data Privacy and Security

- All uploaded documents are processed locally
- No sensitive information is shared with external services except for the Sarvam model API
- Session data is stored temporarily and can be cleared

## YouTube Integration

The chatbot integrates with Avyan AI's YouTube channel:
- Channel URL: https://www.youtube.com/@AvyanAI
- Searches for relevant training videos based on user queries
- Provides direct links to educational content

## Next Steps

1. Add your internal documentation to the `documents/` folder
2. Configure YouTube API key in the backend `.env` file
3. Test the chatbot with sample queries
4. Monitor performance and user interactions

For technical support, refer to the main project documentation.
