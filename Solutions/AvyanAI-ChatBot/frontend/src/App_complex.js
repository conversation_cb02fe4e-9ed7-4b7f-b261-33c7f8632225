import React, { useState, useRef, useEffect } from 'react';
import './App.css';
import { 
  Box, 
  AppBar, 
  Toolbar, 
  Typography, 
  Paper, 
  Avatar, 
  TextField, 
  IconButton, 
  Button, 
  CircularProgress,
  Chip,
  Fade,
  Slide,
  Container,
  Card,
  Divider,
  Stack,
  Tooltip
} from '@mui/material';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import MicIcon from '@mui/icons-material/Mic';
import StopIcon from '@mui/icons-material/Stop';
import SendIcon from '@mui/icons-material/Send';
import PersonIcon from '@mui/icons-material/Person';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import RecordVoiceOverIcon from '@mui/icons-material/RecordVoiceOver';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import StopCircleIcon from '@mui/icons-material/StopCircle';
import TextToSpeechIcon from '@mui/icons-material/RecordVoiceOver';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import LanguageIcon from '@mui/icons-material/Language';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import { Select, MenuItem, FormControl, InputLabel } from '@mui/material';

// Use HTTPS for API if frontend is running on HTTPS, otherwise HTTP
const API_BASE_URL = window.location.protocol === 'https:'
  ? 'https://localhost:8000/api'
  : 'http://localhost:8000/api';

function App() {
  const [messages, setMessages] = useState([
    {
      from: 'ai',
      text: "Hello! I'm your Avyan AI assistant. I can help you with information about our services, team, case studies, and technologies. Feel free to ask me anything via text or voice!",
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [listening, setListening] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [currentAudio, setCurrentAudio] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [ttsEnabled, setTtsEnabled] = useState(true);
  const [selectedLanguage, setSelectedLanguage] = useState('en');
  const [supportedLanguages, setSupportedLanguages] = useState({});
  const [browserInfo, setBrowserInfo] = useState({});
  
  const recognitionRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);

  // Browser compatibility detection
  useEffect(() => {
    const detectBrowser = () => {
      const userAgent = navigator.userAgent;
      let browserName = 'Unknown';
      let isCompatible = true;
      let features = {
        speechRecognition: false,
        speechSynthesis: false,
        mediaRecorder: false,
        webAudio: false
      };

      // Detect browser
      if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
        browserName = 'Safari';
      } else if (userAgent.includes('Chrome')) {
        browserName = 'Chrome';
      } else if (userAgent.includes('Firefox')) {
        browserName = 'Firefox';
      } else if (userAgent.includes('Edge')) {
        browserName = 'Edge';
      }

      // Check feature support
      features.speechRecognition = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
      features.speechSynthesis = 'speechSynthesis' in window;
      features.mediaRecorder = 'MediaRecorder' in window;
      features.webAudio = 'AudioContext' in window || 'webkitAudioContext' in window;

      setBrowserInfo({
        name: browserName,
        userAgent,
        isCompatible,
        features
      });
    };

    detectBrowser();
  }, []);

  // Load supported languages
  useEffect(() => {
    const loadLanguages = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/languages`);
        if (response.ok) {
          const data = await response.json();
          setSupportedLanguages(data.languages);
        }
      } catch (error) {
        console.error('Failed to load languages:', error);
      }
    };

    loadLanguages();
  }, []);
  const chatMessagesRef = useRef(null);

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onstart = () => {
        setListening(true);
        setIsRecording(true);
      };

      recognitionRef.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInput(transcript);
        sendMessage(transcript);
      };

      recognitionRef.current.onend = () => {
        setListening(false);
        setIsRecording(false);
      };

      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setListening(false);
        setIsRecording(false);
        
        // Handle different error types
        if (event.error === 'not-allowed' || event.error === 'service-not-allowed') {
          console.warn('Speech recognition not allowed. Please enable microphone permissions or use HTTPS.');
          setErrorMessage('🎤 Microphone access denied. Please enable microphone permissions in your browser settings or use HTTPS for voice features.');
          setTimeout(() => setErrorMessage(''), 5000);
          return;
        }
        
        // For other errors, try the fallback method
        startAdvancedRecording();
      };
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Advanced audio recording with MediaRecorder API (fallback)
  const startAdvancedRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        await transcribeAudio(audioBlob);
        
        // Stop all tracks to release microphone
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
      setListening(true);
    } catch (error) {
      console.error('Error starting advanced recording:', error);
      setErrorMessage('🎤 Unable to access microphone. Please check your browser permissions and try again.');
      setTimeout(() => setErrorMessage(''), 5000);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setListening(false);
    }
  };

  const toggleAudio = () => {
    setAudioEnabled(!audioEnabled);
    // Stop current audio if disabling
    if (audioEnabled && currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
    }
  };

  const toggleTTS = () => {
    setTtsEnabled(!ttsEnabled);
    // Stop any ongoing speech synthesis if disabling TTS
    if (ttsEnabled && 'speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
  };

  const handleDocumentUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);

    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/upload-document`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(prev => [...prev, {
          from: 'ai',
          text: `✅ Document "${file.name}" uploaded successfully! I can now answer questions based on this document.`,
          timestamp: new Date()
        }]);
      } else {
        throw new Error('Upload failed');
      }
    } catch (error) {
      setMessages(prev => [...prev, {
        from: 'ai',
        text: `❌ Failed to upload document "${file.name}". Please try again.`,
        timestamp: new Date()
      }]);
    } finally {
      setLoading(false);
      event.target.value = ''; // Reset file input
    }
  };

  const stopCurrentAudio = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
    }
  };

  const playResponseAudio = async (text) => {
    if (!audioEnabled) return;
    
    try {
      // Stop any currently playing audio
      stopCurrentAudio();
      
      const response = await fetch(`${API_BASE_URL}/speak`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text, lang: 'en' }),
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);
        
        setCurrentAudio(audio);
        
        audio.onended = () => {
          setCurrentAudio(null);
          URL.revokeObjectURL(audioUrl);
        };
        
        audio.onerror = () => {
          console.error('Error playing audio');
          setCurrentAudio(null);
          URL.revokeObjectURL(audioUrl);
        };
        
        await audio.play();
      }
    } catch (error) {
      console.error('Error playing response audio:', error);
    }
  };

  // Transcribe audio using backend API
  const transcribeAudio = async (audioBlob) => {
    try {
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.wav');
      if (sessionId) {
        formData.append('session_id', sessionId);
      }

      const response = await fetch(`${API_BASE_URL}/transcribe`, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Transcription failed');
      }

      const data = await response.json();
      setInput(data.transcription);
      setSessionId(data.session_id);
      sendMessage(data.transcription);
    } catch (error) {
      console.error('Transcription error:', error);
      
      // Show user-friendly error message
      const errorMessage = {
        from: 'ai',
        text: 'I had trouble processing your voice message. Please try typing your message instead.',
        timestamp: new Date()
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  const requestMicrophonePermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop()); // Stop the stream immediately
      return true;
    } catch (error) {
      console.error('Microphone permission denied:', error);
      setErrorMessage('🎤 Microphone permission is required for voice features. Please allow microphone access and try again.');
      setTimeout(() => setErrorMessage(''), 5000);
      return false;
    }
  };

  const startListening = async () => {
    // First, request microphone permission
    const hasPermission = await requestMicrophonePermission();
    if (!hasPermission) {
      return;
    }

    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      if (recognitionRef.current) {
        try {
          recognitionRef.current.start();
        } catch (error) {
          console.error('Speech recognition start error:', error);
          startAdvancedRecording();
        }
      }
    } else {
      // Use MediaRecorder as primary method
      startAdvancedRecording();
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      setListening(false);
    }
    if (isRecording) {
      stopRecording();
    }
  };

  // Enhanced text-to-speech
  const speak = (text) => {
    try {
      if ('speechSynthesis' in window && text) {
        // Only speak if triggered by user action
        window.speechSynthesis.cancel(); // Stop any ongoing speech
        const utterance = new window.SpeechSynthesisUtterance(text);
        window.speechSynthesis.speak(utterance);
      }
    } catch (err) {
      // Gracefully handle playback errors
      console.warn('Speech synthesis error:', err);
    }
  };

  // Enhanced send message function
  const sendMessage = async (msg = input) => {
    if (!msg.trim()) return;
    
    const userMessage = { from: 'user', text: msg, timestamp: new Date() };
    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setLoading(true);
    setIsTyping(true);
    
    try {
      const requestBody = {
        message: msg,
        context: messages.slice(-5), // Send last 5 messages for context
        session_id: sessionId,
        use_voice: true
      };

      const res = await fetch(`${API_BASE_URL}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!res.ok) {
        throw new Error(`Server error: ${res.status}`);
      }

      const data = await res.json();
      
      // Update session ID
      if (data.session_id) {
        setSessionId(data.session_id);
      }

      // Add bot response
      const botMessage = {
        from: 'ai',
        text: data.response || 'Sorry, I could not process that.',
        timestamp: new Date(),
        sources: data.sources || [],
        videos: data.suggested_videos || []
      };

      setMessages((prev) => [...prev, botMessage]);

      // Play audio response if both audio and TTS are enabled
      if (audioEnabled && ttsEnabled) {
        await playResponseAudio(botMessage.text);
      }

    } catch (e) {
      console.error('Chat error:', e);
      const errorMessage = {
        from: 'ai',
        text: 'I apologize, but I\'m experiencing technical difficulties. Please try again in a moment.',
        timestamp: new Date()
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setLoading(false);
      setIsTyping(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (input.trim()) {
      sendMessage(input.trim());
    }
  };



  // Scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
    }
  }, [messages]);

  return (
    <Box sx={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* Error Notification */}
      {errorMessage && (
        <Box
          sx={{
            position: 'fixed',
            top: 20,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 9999,
            backgroundColor: 'rgba(244, 67, 54, 0.9)',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
            backdropFilter: 'blur(10px)',
            maxWidth: '90%',
            textAlign: 'center',
            fontSize: '14px',
            fontWeight: 500
          }}
        >
          {errorMessage}
        </Box>
      )}
      
      {/* Animated background elements */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        opacity: 0.1,
        background: `
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%)
        `
      }} />

      {/* Modern Header */}
      <AppBar 
        position="static" 
        elevation={0}
        sx={{ 
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
        }}
      >
        <Toolbar sx={{ py: 1 }}>
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: 2,
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '50px',
            px: 2,
            py: 0.5
          }}>
            <AutoAwesomeIcon sx={{ color: '#fff', fontSize: 28 }} />
            <Typography variant="h6" sx={{ 
              color: '#fff', 
              fontWeight: 600,
              background: 'linear-gradient(45deg, #fff, #e0e7ff)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              Avyan AI Assistant
            </Typography>
          </Box>
          <Box sx={{ flexGrow: 1 }} />
          
          {/* Audio Output Toggle */}
          <Tooltip title={audioEnabled ? "Disable audio responses" : "Enable audio responses"} arrow>
            <IconButton
              onClick={toggleAudio}
              sx={{
                color: '#fff',
                mr: 2,
                backgroundColor: audioEnabled ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)',
                '&:hover': {
                  backgroundColor: audioEnabled ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)',
                }
              }}
            >
              {audioEnabled ? <VolumeUpIcon /> : <VolumeOffIcon />}
            </IconButton>
          </Tooltip>

          {/* TTS Toggle */}
          <Tooltip title={ttsEnabled ? "Disable auto text-to-speech (read responses yourself)" : "Enable auto text-to-speech"} arrow>
            <IconButton
              onClick={toggleTTS}
              sx={{
                color: '#fff',
                mr: 2,
                backgroundColor: ttsEnabled ? 'rgba(33, 150, 243, 0.2)' : 'rgba(158, 158, 158, 0.2)',
                '&:hover': {
                  backgroundColor: ttsEnabled ? 'rgba(33, 150, 243, 0.3)' : 'rgba(158, 158, 158, 0.3)',
                }
              }}
            >
              {ttsEnabled ? <TextToSpeechIcon /> : <TextFieldsIcon />}
            </IconButton>
          </Tooltip>

          {/* Stop Audio Button */}
          {currentAudio && (
            <Tooltip title="Stop current audio" arrow>
              <IconButton
                onClick={stopCurrentAudio}
                sx={{
                  color: '#fff',
                  mr: 2,
                  backgroundColor: 'rgba(255, 152, 0, 0.2)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 152, 0, 0.3)',
                  }
                }}
              >
                <StopCircleIcon />
              </IconButton>
            </Tooltip>
          )}
          
          <Chip 
            icon={<RecordVoiceOverIcon />}
            label="Voice Enabled"
            variant="outlined"
            sx={{ 
              color: '#fff',
              borderColor: 'rgba(255, 255, 255, 0.3)',
              '& .MuiChip-icon': { color: '#fff' }
            }}
          />
        </Toolbar>
      </AppBar>

      {/* Voice Feature Info */}
      {window.location.protocol !== 'https:' && window.location.hostname !== 'localhost' && (
        <Box
          sx={{
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            borderBottom: '1px solid rgba(255, 193, 7, 0.3)',
            padding: '8px 16px',
            textAlign: 'center'
          }}
        >
          <Typography variant="caption" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
            ℹ️ Voice features require HTTPS or localhost for microphone access
          </Typography>
        </Box>
      )}

      {/* Main Chat Container */}
      <Container maxWidth="lg" sx={{ py: 4, position: 'relative', zIndex: 1 }}>
        <Fade in timeout={800}>
          <Card sx={{ 
            maxWidth: 900,
            mx: 'auto',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
            overflow: 'hidden'
          }}>
            {/* Chat Messages Area */}
            <Box sx={{ 
              height: '70vh',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <Box 
                ref={chatMessagesRef}
                sx={{ 
                  flex: 1,
                  overflowY: 'auto',
                  p: 3,
                  background: 'linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9))'
                }}
              >
                <Stack spacing={3}>
                  {messages.map((msg, idx) => (
                    <Slide key={idx} direction={msg.from === 'user' ? 'left' : 'right'} in timeout={300}>
                      <Box sx={{ 
                        display: 'flex',
                        justifyContent: msg.from === 'user' ? 'flex-end' : 'flex-start',
                        alignItems: 'flex-start',
                        gap: 2
                      }}>
                        {msg.from === 'ai' && (
                          <Avatar sx={{ 
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            width: 40,
                            height: 40,
                            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)'
                          }}>
                            <SmartToyIcon />
                          </Avatar>
                        )}
                        
                        <Paper sx={{
                          maxWidth: '70%',
                          p: 2.5,
                          borderRadius: msg.from === 'user' ? '20px 20px 4px 20px' : '20px 20px 20px 4px',
                          background: msg.from === 'user' 
                            ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                            : 'rgba(255, 255, 255, 0.9)',
                          color: msg.from === 'user' ? '#fff' : '#2d3748',
                          border: msg.from === 'ai' ? '1px solid rgba(0, 0, 0, 0.05)' : 'none',
                          boxShadow: msg.from === 'user' 
                            ? '0 4px 12px rgba(102, 126, 234, 0.3)'
                            : '0 2px 8px rgba(0, 0, 0, 0.1)',
                          position: 'relative'
                        }}>
                          <Typography variant="body1" sx={{ 
                            lineHeight: 1.6,
                            fontSize: '0.95rem'
                          }}>
                            {msg.text}
                          </Typography>
                          
                          {msg.from === 'ai' && (
                            <IconButton 
                              size="small"
                              onClick={() => speak(msg.text)}
                              sx={{ 
                                position: 'absolute',
                                top: 4,
                                right: 4,
                                opacity: 0.6,
                                '&:hover': { opacity: 1 }
                              }}
                            >
                              <VolumeUpIcon fontSize="small" />
                            </IconButton>
                          )}
                          
                          <Typography variant="caption" sx={{ 
                            display: 'block',
                            mt: 1,
                            opacity: 0.7,
                            fontSize: '0.75rem'
                          }}>
                            {msg.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </Typography>
                        </Paper>

                        {msg.from === 'user' && (
                          <Avatar sx={{ 
                            background: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',
                            width: 40,
                            height: 40,
                            boxShadow: '0 4px 12px rgba(72, 187, 120, 0.3)'
                          }}>
                            <PersonIcon />
                          </Avatar>
                        )}
                      </Box>
                    </Slide>
                  ))}
                  
                  {/* Typing Indicator */}
                  {isTyping && (
                    <Fade in>
                      <Box sx={{ 
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2
                      }}>
                        <Avatar sx={{ 
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          width: 40,
                          height: 40
                        }}>
                          <SmartToyIcon />
                        </Avatar>
                        <Paper sx={{
                          p: 2,
                          borderRadius: '20px 20px 20px 4px',
                          background: 'rgba(255, 255, 255, 0.9)',
                          border: '1px solid rgba(0, 0, 0, 0.05)'
                        }}>
                          <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
                            <Typography variant="body2" color="text.secondary">
                              AI is thinking
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 0.5, ml: 1 }}>
                              {[0, 1, 2].map((i) => (
                                <Box
                                  key={i}
                                  sx={{
                                    width: 6,
                                    height: 6,
                                    borderRadius: '50%',
                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                                    animation: `pulse 1.4s infinite ease-in-out`,
                                    animationDelay: `${i * 0.2}s`,
                                    '@keyframes pulse': {
                                      '0%, 60%, 100%': {
                                        transform: 'scale(1)',
                                        opacity: 0.5
                                      },
                                      '30%': {
                                        transform: 'scale(1.2)',
                                        opacity: 1
                                      }
                                    }
                                  }}
                                />
                              ))}
                            </Box>
                          </Box>
                        </Paper>
                      </Box>
                    </Fade>
                  )}
                </Stack>
              </Box>

              <Divider />

              {/* Input Area */}
              <Box sx={{ 
                p: 3,
                background: 'rgba(255, 255, 255, 0.95)'
              }}>
                <form onSubmit={handleSubmit}>
                  <Box sx={{ 
                    display: 'flex',
                    gap: 2,
                    alignItems: 'flex-end'
                  }}>
                    <TextField
                      fullWidth
                      multiline
                      maxRows={3}
                      value={input}
                      onChange={e => setInput(e.target.value)}
                      placeholder="Type your message or click the microphone (requires HTTPS or localhost)..."
                      disabled={loading}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: '25px',
                          background: 'rgba(255, 255, 255, 0.8)',
                          '&:hover fieldset': {
                            borderColor: '#667eea'
                          },
                          '&.Mui-focused fieldset': {
                            borderColor: '#667eea'
                          }
                        }
                      }}
                    />
                    
                    <Tooltip 
                      title={listening ? "Stop recording" : "Start voice recording (requires microphone permission)"}
                      arrow
                    >
                      <IconButton 
                        onClick={listening ? stopListening : startListening}
                        disabled={loading}
                      sx={{
                        width: 56,
                        height: 56,
                        background: listening 
                          ? 'linear-gradient(135deg, #e53e3e 0%, #c53030 100%)'
                          : 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',
                        color: '#fff',
                        '&:hover': {
                          background: listening 
                            ? 'linear-gradient(135deg, #c53030 0%, #9c2626 100%)'
                            : 'linear-gradient(135deg, #38a169 0%, #2f855a 100%)',
                          transform: 'scale(1.05)'
                        },
                        '&:disabled': {
                          background: '#e2e8f0',
                          color: '#a0aec0'
                        },
                        transition: 'all 0.2s ease',
                        boxShadow: listening 
                          ? '0 0 20px rgba(229, 62, 62, 0.4)'
                          : '0 4px 12px rgba(72, 187, 120, 0.3)'
                      }}
                    >
                        {listening ? <StopIcon /> : <MicIcon />}
                      </IconButton>
                    </Tooltip>
                    
                    <Button 
                      type="submit"
                      variant="contained"
                      disabled={loading || !input.trim()}
                      endIcon={loading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                      sx={{
                        height: 56,
                        px: 3,
                        borderRadius: '28px',
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)',
                          transform: 'translateY(-1px)',
                          boxShadow: '0 6px 16px rgba(102, 126, 234, 0.4)'
                        },
                        '&:disabled': {
                          background: '#e2e8f0',
                          color: '#a0aec0'
                        },
                        transition: 'all 0.2s ease'
                      }}
                    >
                      Send
                    </Button>
                  </Box>
                </form>
              </Box>
            </Box>
          </Card>
        </Fade>
      </Container>
    </Box>
  );
}

export default App;
