"""
Automated PDF Processing Pipeline for Avyan AI Chatbot
Handles PDF ingestion, preprocessing, chunking, embedding, and vector storage
"""

import os
import logging
import asyncio
import hashlib
import json
import time
import io
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, asdict

# Document processing imports
import fitz  # PyMuPDF
import pdfplumber
import pytesseract
from PIL import Image
import pandas as pd
import numpy as np

# Vector database and embeddings
import chromadb
from sentence_transformers import SentenceTransformer
import os
# Set offline mode for transformers to avoid network issues
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'

# Langchain imports
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document as LangchainDocument

# File monitoring
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

logger = logging.getLogger(__name__)

@dataclass
class ProcessingMetadata:
    """Metadata for processed documents"""
    file_path: str
    file_hash: str
    processed_at: datetime
    total_pages: int
    total_chunks: int
    extraction_method: str
    has_tables: bool
    has_images: bool
    file_size: int

class PDFProcessor:
    """Enhanced PDF processor with OCR fallback and table extraction"""
    
    def __init__(self):
        self.ocr_enabled = self._check_tesseract()
        
    def _check_tesseract(self) -> bool:
        """Check if Tesseract OCR is available"""
        try:
            pytesseract.get_tesseract_version()
            return True
        except Exception:
            logger.warning("Tesseract OCR not available. OCR fallback disabled.")
            return False
    
    async def extract_text_pymupdf(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Extract text using PyMuPDF with enhanced metadata"""
        text_content = ""
        metadata = {
            "total_pages": 0,
            "has_tables": False,
            "has_images": False,
            "extraction_method": "pymupdf"
        }
        
        try:
            doc = fitz.open(file_path)
            metadata["total_pages"] = len(doc)
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # Extract text
                page_text = page.get_text()
                
                # Check for tables (simple heuristic)
                if page_text.count('\t') > 10 or page_text.count('|') > 5:
                    metadata["has_tables"] = True
                
                # Check for images
                image_list = page.get_images()
                if image_list:
                    metadata["has_images"] = True
                
                if page_text.strip():
                    text_content += f"\n--- Page {page_num + 1} ---\n{page_text}"
                
                # OCR fallback for pages with little text but images
                elif self.ocr_enabled and image_list:
                    try:
                        pix = page.get_pixmap()
                        img_data = pix.tobytes("png")
                        img = Image.open(io.BytesIO(img_data))
                        ocr_text = pytesseract.image_to_string(img)
                        if ocr_text.strip():
                            text_content += f"\n--- Page {page_num + 1} (OCR) ---\n{ocr_text}"
                            metadata["extraction_method"] = "pymupdf+ocr"
                    except Exception as e:
                        logger.warning(f"OCR failed for page {page_num + 1}: {e}")
            
            doc.close()
            
        except Exception as e:
            logger.error(f"PyMuPDF extraction failed: {e}")
            raise
        
        return text_content, metadata
    
    async def extract_text_pdfplumber(self, file_path: Path) -> Tuple[str, Dict[str, Any]]:
        """Extract text using pdfplumber with table detection"""
        text_content = ""
        metadata = {
            "total_pages": 0,
            "has_tables": False,
            "has_images": False,
            "extraction_method": "pdfplumber"
        }
        
        try:
            with pdfplumber.open(file_path) as pdf:
                metadata["total_pages"] = len(pdf.pages)
                
                for page_num, page in enumerate(pdf.pages):
                    # Extract regular text
                    page_text = page.extract_text()
                    
                    # Extract tables
                    tables = page.extract_tables()
                    if tables:
                        metadata["has_tables"] = True
                        table_text = ""
                        for table in tables:
                            # Convert table to text format
                            for row in table:
                                if row:
                                    table_text += " | ".join([str(cell) if cell else "" for cell in row]) + "\n"
                        page_text = (page_text or "") + f"\n\nTables on page {page_num + 1}:\n{table_text}"
                    
                    if page_text and page_text.strip():
                        text_content += f"\n--- Page {page_num + 1} ---\n{page_text}"
        
        except Exception as e:
            logger.error(f"PDFPlumber extraction failed: {e}")
            raise
        
        return text_content, metadata
    
    async def process_pdf(self, file_path: Path) -> Tuple[str, ProcessingMetadata]:
        """Process PDF with multiple extraction methods"""
        text_content = ""
        processing_metadata = None
        
        # Try pdfplumber first (better for tables)
        try:
            text_content, metadata = await self.extract_text_pdfplumber(file_path)
            if text_content.strip():
                processing_metadata = ProcessingMetadata(
                    file_path=str(file_path),
                    file_hash=self._get_file_hash(file_path),
                    processed_at=datetime.now(),
                    total_pages=metadata["total_pages"],
                    total_chunks=0,  # Will be updated later
                    extraction_method=metadata["extraction_method"],
                    has_tables=metadata["has_tables"],
                    has_images=metadata["has_images"],
                    file_size=file_path.stat().st_size
                )
        except Exception as e:
            logger.warning(f"PDFPlumber failed, trying PyMuPDF: {e}")
        
        # Fallback to PyMuPDF if pdfplumber fails or produces no text
        if not text_content.strip():
            try:
                text_content, metadata = await self.extract_text_pymupdf(file_path)
                if processing_metadata:
                    processing_metadata.extraction_method = metadata["extraction_method"]
                    processing_metadata.has_images = metadata["has_images"]
                else:
                    processing_metadata = ProcessingMetadata(
                        file_path=str(file_path),
                        file_hash=self._get_file_hash(file_path),
                        processed_at=datetime.now(),
                        total_pages=metadata["total_pages"],
                        total_chunks=0,
                        extraction_method=metadata["extraction_method"],
                        has_tables=metadata["has_tables"],
                        has_images=metadata["has_images"],
                        file_size=file_path.stat().st_size
                    )
            except Exception as e:
                logger.error(f"All PDF extraction methods failed: {e}")
                raise
        
        return text_content, processing_metadata
    
    def _get_file_hash(self, file_path: Path) -> str:
        """Get MD5 hash of a file"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

class IntelligentChunker:
    """Intelligent text chunking with semantic boundary detection"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", ". ", " ", ""]
        )
    
    def create_chunks(self, text: str, metadata: ProcessingMetadata) -> List[LangchainDocument]:
        """Create intelligent chunks with enhanced metadata"""
        documents = []
        
        # Split text into chunks
        chunks = self.text_splitter.split_text(text)
        
        # Update total chunks in metadata
        metadata.total_chunks = len(chunks)
        
        for i, chunk in enumerate(chunks):
            # Extract page information from chunk
            page_info = self._extract_page_info(chunk)
            
            # Create enhanced metadata for each chunk
            chunk_metadata = {
                "source": metadata.file_path,
                "type": "pdf",
                "chunk_id": i,
                "total_chunks": len(chunks),
                "page_numbers": ",".join(map(str, page_info["pages"])) if page_info["pages"] else "",
                "page_range": page_info["page_range"],
                "extraction_method": metadata.extraction_method,
                "has_tables": metadata.has_tables,
                "has_images": metadata.has_images,
                "file_size": metadata.file_size,
                "processed_at": metadata.processed_at.isoformat(),
                "chunk_size": len(chunk),
                "file_hash": metadata.file_hash
            }
            
            documents.append(LangchainDocument(
                page_content=chunk,
                metadata=chunk_metadata
            ))
        
        return documents
    
    def _extract_page_info(self, chunk: str) -> Dict[str, Any]:
        """Extract page information from chunk text"""
        import re
        page_pattern = r"--- Page (\d+)"
        pages = re.findall(page_pattern, chunk)
        return {
            "pages": [int(p) for p in pages] if pages else [],
            "page_range": f"{min(pages)}-{max(pages)}" if len(pages) > 1 else pages[0] if pages else "unknown"
        }

class FileWatcher(FileSystemEventHandler):
    """File system event handler for monitoring PDF changes"""

    def __init__(self, pipeline_manager):
        self.pipeline_manager = pipeline_manager
        self.debounce_time = 2.0  # seconds
        self.pending_files = {}

    def on_created(self, event):
        if not event.is_directory and event.src_path.endswith('.pdf'):
            logger.info(f"New PDF detected: {event.src_path}")
            self._schedule_processing(event.src_path)

    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('.pdf'):
            logger.info(f"PDF modified: {event.src_path}")
            self._schedule_processing(event.src_path)

    def on_deleted(self, event):
        if not event.is_directory and event.src_path.endswith('.pdf'):
            logger.info(f"PDF deleted: {event.src_path}")
            asyncio.create_task(self.pipeline_manager.remove_document(event.src_path))

    def _schedule_processing(self, file_path: str):
        """Schedule file processing with debouncing"""
        self.pending_files[file_path] = time.time()

        # Schedule processing after debounce time
        asyncio.create_task(self._process_after_delay(file_path))

    async def _process_after_delay(self, file_path: str):
        """Process file after debounce delay"""
        await asyncio.sleep(self.debounce_time)

        # Check if file is still pending and hasn't been updated recently
        if (file_path in self.pending_files and
            time.time() - self.pending_files[file_path] >= self.debounce_time):

            del self.pending_files[file_path]
            await self.pipeline_manager.process_single_file(Path(file_path))

class PDFPipelineManager:
    """Main pipeline manager for automated PDF processing"""

    def __init__(self,
                 data_folder: str = "data",
                 services_folder: str = "data/documents/services",
                 embedding_model: str = "all-MiniLM-L6-v2"):

        self.data_folder = Path(data_folder)
        self.services_folder = Path(services_folder)
        self.embedding_model_name = embedding_model

        # Initialize embedding model with offline mode
        try:
            # Try to load model in offline mode first
            self.embedding_model = SentenceTransformer(embedding_model, local_files_only=True)
            logger.info(f"Loaded embedding model {embedding_model} from cache")
        except Exception as e:
            logger.warning(f"Failed to load model from cache: {e}")
            try:
                # Fallback to online mode if cache fails
                self.embedding_model = SentenceTransformer(embedding_model)
                logger.info(f"Loaded embedding model {embedding_model} from online")
            except Exception as e2:
                logger.error(f"Failed to load embedding model: {e2}")
                # Use a simple fallback - we'll create a dummy model for now
                self.embedding_model = None

        # Initialize components
        self.pdf_processor = PDFProcessor()
        self.chunker = IntelligentChunker()

        # Initialize ChromaDB
        self.chroma_client = chromadb.PersistentClient(
            path=str(self.data_folder / "chroma_db"),
            settings=chromadb.config.Settings(anonymized_telemetry=False)
        )

        # Get or create collection for PDF documents
        self.collection = self._get_or_create_collection("pdf_documents")

        # Processing metadata storage
        self.metadata_file = self.data_folder / "pdf_processing_metadata.json"
        self.processing_metadata = self._load_processing_metadata()

        # File watcher
        self.observer = None
        self.file_watcher = FileWatcher(self)

        logger.info(f"PDFPipelineManager initialized for folder: {services_folder}")

    def _get_or_create_collection(self, name: str):
        """Get or create ChromaDB collection"""
        try:
            return self.chroma_client.get_collection(name)
        except Exception:
            return self.chroma_client.create_collection(
                name=name,
                metadata={"hnsw:space": "cosine"}
            )

    def _load_processing_metadata(self) -> Dict[str, Dict]:
        """Load processing metadata from file"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading processing metadata: {e}")
        return {}

    def _save_processing_metadata(self):
        """Save processing metadata to file"""
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.processing_metadata, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving processing metadata: {e}")

    def _should_process_file(self, file_path: Path) -> bool:
        """Check if file should be processed (new or modified)"""
        file_str = str(file_path)
        current_hash = self.pdf_processor._get_file_hash(file_path)

        if file_str not in self.processing_metadata:
            return True

        stored_metadata = self.processing_metadata[file_str]
        return stored_metadata.get("file_hash") != current_hash

    async def process_single_file(self, file_path: Path) -> bool:
        """Process a single PDF file"""
        try:
            if not file_path.exists():
                logger.warning(f"File not found: {file_path}")
                return False

            if not self._should_process_file(file_path):
                logger.info(f"Skipping {file_path} (already processed)")
                return True

            logger.info(f"Processing PDF: {file_path}")

            # Extract text and metadata
            text_content, processing_metadata = await self.pdf_processor.process_pdf(file_path)

            if not text_content.strip():
                logger.warning(f"No text extracted from {file_path}")
                return False

            # Create chunks
            documents = self.chunker.create_chunks(text_content, processing_metadata)

            if not documents:
                logger.warning(f"No chunks created from {file_path}")
                return False

            # Remove existing documents for this file
            await self.remove_document(str(file_path))

            # Index documents
            await self._index_documents(documents)

            # Store processing metadata
            self.processing_metadata[str(file_path)] = asdict(processing_metadata)
            self._save_processing_metadata()

            logger.info(f"Successfully processed {file_path}: {len(documents)} chunks indexed")
            return True

        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            return False

    async def _index_documents(self, documents: List[LangchainDocument]):
        """Index documents in ChromaDB"""
        try:
            # Prepare data for ChromaDB
            texts = [doc.page_content for doc in documents]
            metadatas = [doc.metadata for doc in documents]
            ids = [f"{doc.metadata['source']}_{doc.metadata['chunk_id']}" for doc in documents]

            # Generate embeddings
            if self.embedding_model is None:
                logger.error("Embedding model not available, cannot add documents")
                return
            embeddings = self.embedding_model.encode(texts).tolist()

            # Add to collection
            self.collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )

            logger.info(f"Indexed {len(documents)} document chunks")

        except Exception as e:
            logger.error(f"Error indexing documents: {e}")
            raise

    async def remove_document(self, file_path: str):
        """Remove document from vector store"""
        try:
            # Get all document IDs for this file
            results = self.collection.get(
                where={"source": file_path},
                include=["metadatas"]
            )

            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"Removed {len(results['ids'])} chunks for {file_path}")

            # Remove from processing metadata
            if file_path in self.processing_metadata:
                del self.processing_metadata[file_path]
                self._save_processing_metadata()

        except Exception as e:
            logger.error(f"Error removing document {file_path}: {e}")

    async def process_all_pdfs(self) -> Dict[str, bool]:
        """Process all PDF files in the services folder"""
        results = {}

        if not self.services_folder.exists():
            logger.error(f"Services folder not found: {self.services_folder}")
            return results

        pdf_files = list(self.services_folder.glob("*.pdf"))
        logger.info(f"Found {len(pdf_files)} PDF files to process")

        for pdf_file in pdf_files:
            try:
                success = await self.process_single_file(pdf_file)
                results[str(pdf_file)] = success
            except Exception as e:
                logger.error(f"Failed to process {pdf_file}: {e}")
                results[str(pdf_file)] = False

        return results

    def start_monitoring(self):
        """Start file system monitoring"""
        if self.observer is not None:
            logger.warning("File monitoring already started")
            return

        self.observer = Observer()
        self.observer.schedule(
            self.file_watcher,
            str(self.services_folder),
            recursive=False
        )
        self.observer.start()
        logger.info(f"Started monitoring {self.services_folder}")

    def stop_monitoring(self):
        """Stop file system monitoring"""
        if self.observer is not None:
            self.observer.stop()
            self.observer.join()
            self.observer = None
            logger.info("Stopped file monitoring")

    async def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant document chunks"""
        try:
            # Generate query embedding
            if self.embedding_model is None:
                logger.error("Embedding model not available, cannot search")
                return []
            query_embedding = self.embedding_model.encode([query]).tolist()[0]

            # Search in collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=['documents', 'metadatas', 'distances']
            )

            # Process results
            search_results = []
            for i in range(len(results['documents'][0])):
                search_results.append({
                    'content': results['documents'][0][i],
                    'metadata': results['metadatas'][0][i],
                    'score': 1 - results['distances'][0][i],  # Convert distance to similarity
                    'collection': 'pdf_documents'
                })

            return search_results

        except Exception as e:
            logger.error(f"Error in search: {e}")
            return []

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        total_files = len(self.processing_metadata)
        total_chunks = sum(metadata.get('total_chunks', 0) for metadata in self.processing_metadata.values())

        # Get collection stats
        try:
            collection_count = self.collection.count()
        except:
            collection_count = 0

        return {
            "total_files_processed": total_files,
            "total_chunks_created": total_chunks,
            "total_chunks_indexed": collection_count,
            "files_with_tables": sum(1 for m in self.processing_metadata.values() if m.get('has_tables', False)),
            "files_with_images": sum(1 for m in self.processing_metadata.values() if m.get('has_images', False)),
            "extraction_methods": {
                method: sum(1 for m in self.processing_metadata.values() if m.get('extraction_method') == method)
                for method in ['pdfplumber', 'pymupdf', 'pymupdf+ocr']
            }
        }

    async def refresh_all(self) -> Dict[str, Any]:
        """Refresh all documents (reprocess everything)"""
        logger.info("Starting full refresh of all PDF documents")

        # Clear existing data
        try:
            self.chroma_client.delete_collection("pdf_documents")
            self.collection = self._get_or_create_collection("pdf_documents")
            logger.info("Cleared existing document index")
        except Exception as e:
            logger.error(f"Error clearing collection: {e}")

        # Clear processing metadata
        self.processing_metadata = {}
        self._save_processing_metadata()

        # Reprocess all files
        results = await self.process_all_pdfs()

        stats = self.get_processing_stats()
        logger.info(f"Refresh completed. Stats: {stats}")

        return {
            "processing_results": results,
            "stats": stats
        }

# Convenience function for easy integration
async def create_pdf_pipeline(data_folder: str = "data",
                            services_folder: str = "data/documents/services") -> PDFPipelineManager:
    """Create and initialize PDF pipeline"""
    pipeline = PDFPipelineManager(data_folder, services_folder)
    return pipeline
