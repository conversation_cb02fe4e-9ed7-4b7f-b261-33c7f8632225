import React, { useState, useRef, useEffect } from 'react';
import './App.css';
import {
  Container,
  Box,
  AppBar,
  Toolbar,
  Typography,
  Paper,
  Avatar,
  TextField,
  IconButton,
  CircularProgress,
  Chip,
  Fade,
  Tooltip,
  Stack,
  Select,
  MenuItem,
  FormControl
} from '@mui/material';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import MicIcon from '@mui/icons-material/Mic';
import StopIcon from '@mui/icons-material/Stop';
import SendIcon from '@mui/icons-material/Send';
import PersonIcon from '@mui/icons-material/Person';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import StopCircleIcon from '@mui/icons-material/StopCircle';
import TextToSpeechIcon from '@mui/icons-material/RecordVoiceOver';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import Link from '@mui/material/Link';

// API URL with HTTPS support
const API_BASE_URL = 'https://localhost:8000/api';

function App() {
  const [messages, setMessages] = useState([
    {
      from: 'ai',
      text: "Hello! I'm your Avyan AI assistant. I can help you with information about our services, team, case studies, and technologies. Feel free to ask me anything via text or voice!",
      timestamp: new Date()
    }
  ]);
  const [input, setInput] = useState('');
  const [listening, setListening] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [currentAudio, setCurrentAudio] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [ttsEnabled, setTtsEnabled] = useState(true);
  const [liveTranscript, setLiveTranscript] = useState('');
  const [language, setLanguage] = useState('en');
  const [supportedLanguages, setSupportedLanguages] = useState({ en: 'English', hi: 'हिंदी (Hindi)' });

  const recognitionRef = useRef(null);

  // Initialize speech recognition with live transcription
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.webkitSpeechRecognition || window.SpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = (event) => {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        // Update live transcript for tab title
        if (interimTranscript) {
          setLiveTranscript(interimTranscript);
        }

        // When final result is available, set it to input and stop
        if (finalTranscript) {
          setInput(finalTranscript);
          setListening(false);
          setLiveTranscript('');
        }
      };

      recognitionRef.current.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        setListening(false);
        setLiveTranscript('');
        setErrorMessage('Speech recognition failed. Please try again.');
      };

      recognitionRef.current.onend = () => {
        setListening(false);
        setLiveTranscript('');
      };

      recognitionRef.current.onstart = () => {
        setListening(true);
        setErrorMessage('');
      };
    }
  }, []);

  // Update document title with live transcription
  useEffect(() => {
    if (listening && liveTranscript) {
      document.title = `🎤 "${liveTranscript}" - Avyan AI`;
    } else if (listening) {
      document.title = '🎤 Listening... - Avyan AI';
    } else {
      document.title = 'Avyan AI Assistant';
    }
  }, [listening, liveTranscript]);



  const toggleTTS = () => {
    setTtsEnabled(!ttsEnabled);
    if (ttsEnabled && 'speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
  };

  const stopCurrentAudio = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
    }
  };

  const playResponseAudio = async (text) => {
    if (!audioEnabled) return;
    
    try {
      stopCurrentAudio();
      
      const response = await fetch(`${API_BASE_URL}/speak`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text, lang: 'en' }),
      });

      if (response.ok) {
        const audioBlob = await response.blob();
        const audioUrl = URL.createObjectURL(audioBlob);
        const audio = new Audio(audioUrl);
        
        setCurrentAudio(audio);
        
        audio.onended = () => {
          setCurrentAudio(null);
          URL.revokeObjectURL(audioUrl);
        };
        
        await audio.play();
      }
    } catch (error) {
      console.error('Error playing audio:', error);
    }
  };

  const startListening = () => {
    if (recognitionRef.current && !listening) {
      try {
        setListening(true);
        setErrorMessage('');
        setLiveTranscript('');
        recognitionRef.current.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
        setListening(false);
        setErrorMessage('Failed to start speech recognition. Please try again.');
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && listening) {
      try {
        recognitionRef.current.stop();
        setListening(false);
        setLiveTranscript('');
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
        setListening(false);
        setLiveTranscript('');
      }
    }
  };

  const speak = (text) => {
    try {
      if ('speechSynthesis' in window && text) {
        window.speechSynthesis.cancel();
        const utterance = new window.SpeechSynthesisUtterance(text);
        window.speechSynthesis.speak(utterance);
      }
    } catch (err) {
      console.warn('Speech synthesis error:', err);
    }
  };

  // Function to format message text with clickable links and better formatting
  const formatMessageText = (text) => {
    if (!text) return null;

    // Split text by lines to handle formatting
    const lines = text.split('\n');
    const formattedElements = [];

    lines.forEach((line, lineIndex) => {
      if (line.trim() === '') {
        formattedElements.push(<br key={`br-${lineIndex}`} />);
        return;
      }

      // Handle different formatting patterns
      const elements = [];

      // Check for bold text (markdown-style)
      if (line.includes('**')) {
        const parts = line.split('**');
        parts.forEach((part, partIndex) => {
          if (partIndex % 2 === 1) {
            // Odd indices are bold text
            elements.push(
              <strong key={`bold-${lineIndex}-${partIndex}`} style={{ fontWeight: 600 }}>
                {part}
              </strong>
            );
          } else {
            // Even indices are regular text, check for links
            const linkRegex = /(https?:\/\/[^\s]+)/g;
            const textParts = part.split(linkRegex);

            textParts.forEach((textPart, textIndex) => {
              if (linkRegex.test(textPart)) {
                elements.push(
                  <Link
                    key={`link-${lineIndex}-${partIndex}-${textIndex}`}
                    href={textPart}
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{
                      color: '#1976d2',
                      textDecoration: 'underline',
                      '&:hover': {
                        color: '#1565c0',
                        textDecoration: 'underline'
                      }
                    }}
                  >
                    {textPart}
                  </Link>
                );
              } else if (textPart) {
                elements.push(textPart);
              }
            });
          }
        });
      } else {
        // No bold text, just check for links
        const linkRegex = /(https?:\/\/[^\s]+)/g;
        const parts = line.split(linkRegex);

        parts.forEach((part, partIndex) => {
          if (linkRegex.test(part)) {
            elements.push(
              <Link
                key={`link-${lineIndex}-${partIndex}`}
                href={part}
                target="_blank"
                rel="noopener noreferrer"
                sx={{
                  color: '#1976d2',
                  textDecoration: 'underline',
                  '&:hover': {
                    color: '#1565c0',
                    textDecoration: 'underline'
                  }
                }}
              >
                {part}
              </Link>
            );
          } else if (part) {
            elements.push(part);
          }
        });
      }

      // Add the formatted line with special styling for lists
      if (elements.length > 0) {
        const isListItem = line.startsWith('•') || line.match(/^\d+\./);
        const isBoldHeader = line.includes('**') && (line.includes('Why Choose') || line.includes('Get Started'));

        formattedElements.push(
          <Typography
            key={`line-${lineIndex}`}
            variant="body1"
            component="div"
            sx={{
              mb: isListItem ? 0.5 : (isBoldHeader ? 1.5 : 1),
              mt: isBoldHeader ? 1 : 0,
              lineHeight: 1.6,
              fontSize: '0.95rem',
              pl: isListItem ? 1 : 0,
              fontWeight: isBoldHeader ? 600 : 'normal'
            }}
          >
            {elements}
          </Typography>
        );
      }
    });

    return formattedElements;
  };

  const sendMessage = async (msg = input) => {
    if (!msg.trim()) return;
    
    const userMessage = { from: 'user', text: msg, timestamp: new Date() };
    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setLoading(true);
    setIsTyping(true);
    
    try {
      const requestBody = {
        message: msg,
        context: messages.slice(-5),
        session_id: sessionId,
        use_voice: true,
        language: language
      };

      const res = await fetch(`${API_BASE_URL}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      if (!res.ok) {
        throw new Error(`Server error: ${res.status}`);
      }

      const data = await res.json();
      
      if (data.session_id) {
        setSessionId(data.session_id);
      }

      const botMessage = {
        from: 'ai',
        text: data.response || 'Sorry, I could not process that.',
        timestamp: new Date(),
        sources: data.sources || []
      };

      setMessages((prev) => [...prev, botMessage]);

      // Play audio response if both audio and TTS are enabled
      if (audioEnabled && ttsEnabled) {
        await playResponseAudio(botMessage.text);
      }

    } catch (e) {
      console.error('Error sending message:', e);
      setMessages((prev) => [...prev, {
        from: 'ai',
        text: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      }]);
    } finally {
      setLoading(false);
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="App">
      <AppBar position="static" sx={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
      }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 600 }}>
            Avyan AI Assistant
            {listening && (
              <Typography
                component="span"
                variant="body2"
                sx={{
                  ml: 2,
                  color: '#ffeb3b',
                  fontWeight: 400,
                  fontSize: '0.9rem',
                  animation: 'liveTranscriptionPulse 1.5s infinite'
                }}
              >
                {liveTranscript ? `🎤 "${liveTranscript}"` : '🎤 Listening...'}
              </Typography>
            )}
          </Typography>

          {/* Language Selector */}
          <FormControl size="small" sx={{ mr: 2, minWidth: 120 }}>
            <Select
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              sx={{
                color: '#fff',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                },
                '& .MuiSvgIcon-root': {
                  color: '#fff',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#fff',
                }
              }}
            >
              {Object.entries(supportedLanguages).map(([code, name]) => (
                <MenuItem key={code} value={code}>
                  {name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* TTS Toggle */}
          <Tooltip title={ttsEnabled ? "Disable auto text-to-speech (read responses yourself)" : "Enable auto text-to-speech"} arrow>
            <IconButton
              onClick={toggleTTS}
              sx={{
                color: '#fff',
                mr: 2,
                backgroundColor: ttsEnabled ? 'rgba(33, 150, 243, 0.2)' : 'rgba(158, 158, 158, 0.2)',
                '&:hover': {
                  backgroundColor: ttsEnabled ? 'rgba(33, 150, 243, 0.3)' : 'rgba(158, 158, 158, 0.3)',
                }
              }}
            >
              {ttsEnabled ? <TextToSpeechIcon /> : <TextFieldsIcon />}
            </IconButton>
          </Tooltip>

          {/* Stop Audio Button */}
          {currentAudio && (
            <Tooltip title="Stop current audio" arrow>
              <IconButton
                onClick={stopCurrentAudio}
                sx={{
                  color: '#fff',
                  mr: 2,
                  backgroundColor: 'rgba(255, 152, 0, 0.2)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 152, 0, 0.3)',
                  }
                }}
              >
                <StopCircleIcon />
              </IconButton>
            </Tooltip>
          )}
        </Toolbar>
      </AppBar>

      <Container maxWidth="md" sx={{ mt: 2, mb: 2 }}>
        <Paper 
          elevation={3} 
          sx={{ 
            height: '70vh', 
            display: 'flex', 
            flexDirection: 'column',
            borderRadius: 3,
            overflow: 'hidden',
            background: 'linear-gradient(145deg, #f0f2f5 0%, #ffffff 100%)'
          }}
        >
          {/* Messages Area */}
          <Box sx={{ 
            flexGrow: 1, 
            overflowY: 'auto', 
            p: 2,
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              background: '#f1f1f1',
              borderRadius: '10px',
            },
            '&::-webkit-scrollbar-thumb': {
              background: '#c1c1c1',
              borderRadius: '10px',
            },
            '&::-webkit-scrollbar-thumb:hover': {
              background: '#a8a8a8',
            },
          }}>
            {messages.map((msg, index) => (
              <Fade in={true} timeout={500} key={index}>
                <Box sx={{ 
                  display: 'flex', 
                  justifyContent: msg.from === 'user' ? 'flex-end' : 'flex-start',
                  mb: 2
                }}>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'flex-start',
                    maxWidth: '80%',
                    flexDirection: msg.from === 'user' ? 'row-reverse' : 'row'
                  }}>
                    <Avatar sx={{ 
                      bgcolor: msg.from === 'user' ? '#667eea' : '#764ba2',
                      mx: 1,
                      width: 40,
                      height: 40
                    }}>
                      {msg.from === 'user' ? <PersonIcon /> : <SmartToyIcon />}
                    </Avatar>
                    
                    <Paper 
                      elevation={2}
                      sx={{ 
                        p: 2,
                        position: 'relative',
                        backgroundColor: msg.from === 'user' ? '#667eea' : '#ffffff',
                        color: msg.from === 'user' ? '#ffffff' : '#333333',
                        borderRadius: 3,
                        maxWidth: '100%',
                        wordWrap: 'break-word'
                      }}
                    >
                      <Box sx={{ mb: 1 }}>
                        {formatMessageText(msg.text)}
                      </Box>
                      
                      {msg.from === 'ai' && (
                        <IconButton 
                          size="small"
                          onClick={() => speak(msg.text)}
                          sx={{ 
                            position: 'absolute',
                            top: 4,
                            right: 4,
                            opacity: 0.6,
                            '&:hover': { opacity: 1 }
                          }}
                        >
                          <VolumeUpIcon fontSize="small" />
                        </IconButton>
                      )}
                      
                      <Typography variant="caption" sx={{ 
                        display: 'block',
                        textAlign: 'right',
                        mt: 1,
                        opacity: 0.7
                      }}>
                        {msg.timestamp.toLocaleTimeString()}
                      </Typography>

                      {msg.sources && msg.sources.length > 0 && (
                        <Stack direction="row" spacing={1} sx={{ mt: 1, flexWrap: 'wrap' }}>
                          {msg.sources.slice(0, 3).map((source, idx) => (
                            <Chip 
                              key={idx}
                              label={`Source ${idx + 1}`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.7rem' }}
                            />
                          ))}
                        </Stack>
                      )}
                    </Paper>
                  </Box>
                </Box>
              </Fade>
            ))}
            
            {isTyping && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: '#764ba2', mr: 1, width: 40, height: 40 }}>
                    <SmartToyIcon />
                  </Avatar>
                  <Paper elevation={2} sx={{ p: 2, borderRadius: 3 }}>
                    <Typography variant="body2" sx={{ fontStyle: 'italic', opacity: 0.7 }}>
                      AI is thinking...
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            )}
          </Box>

          {/* Input Area */}
          <Box sx={{ 
            p: 2, 
            borderTop: '1px solid #e0e0e0',
            background: 'linear-gradient(90deg, #f8f9fa 0%, #ffffff 100%)'
          }}>
            {errorMessage && (
              <Typography color="error" variant="body2" sx={{ mb: 1 }}>
                {errorMessage}
              </Typography>
            )}
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TextField
                fullWidth
                multiline
                maxRows={3}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message here..."
                disabled={loading}
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    backgroundColor: '#ffffff'
                  }
                }}
              />
              
              <Tooltip title={listening ? "Stop listening" : "Start voice input"} arrow>
                <IconButton
                  onClick={listening ? stopListening : startListening}
                  disabled={loading}
                  sx={{
                    backgroundColor: listening ? '#ff5722' : '#4caf50',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: listening ? '#d84315' : '#388e3c',
                    },
                    '&:disabled': {
                      backgroundColor: '#cccccc',
                    }
                  }}
                >
                  {listening ? <StopIcon /> : <MicIcon />}
                </IconButton>
              </Tooltip>
              
              <Tooltip title="Send message" arrow>
                <IconButton
                  onClick={() => sendMessage()}
                  disabled={loading || !input.trim()}
                  sx={{
                    backgroundColor: '#667eea',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#5a67d8',
                    },
                    '&:disabled': {
                      backgroundColor: '#cccccc',
                    }
                  }}
                >
                  {loading ? <CircularProgress size={24} color="inherit" /> : <SendIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Paper>
      </Container>
    </div>
  );
}

export default App;
