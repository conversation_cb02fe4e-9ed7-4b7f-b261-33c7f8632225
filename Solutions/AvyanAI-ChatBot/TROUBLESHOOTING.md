# 🔧 Troubleshooting Guide

## 🚨 Common Issues & Solutions

### 1. **Safari Can't Open the Page**
**Problem**: <PERSON>fari shows "server unexpectedly dropped the connection"

**Solutions**:
```bash
# Option 1: Restart both servers
pkill -f 'uvicorn|react-scripts'
./start_chatbot.sh

# Option 2: Manual restart
cd backend && source venv/bin/activate && uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
cd frontend && npm start &

# Option 3: Check if ports are busy
lsof -i :3000
lsof -i :8000
```

### 2. **Port Already in Use**
```bash
# Kill processes on specific ports
lsof -ti:3000 | xargs kill -9
lsof -ti:8000 | xargs kill -9
```

### 3. **Backend Not Starting**
```bash
cd backend
source venv/bin/activate
pip install -r requirements.txt
python main.py
```

### 4. **Frontend Not Starting**
```bash
cd frontend
npm install
npm start
```

### 5. **Audio Not Working**
- Check browser permissions for microphone
- Ensure speakers/headphones are connected
- Try refreshing the page

## 🧪 Quick System Test

```bash
# Test backend
curl http://localhost:8000/health

# Test chat
curl -X POST "http://localhost:8000/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "context": [], "session_id": null, "use_voice": false}'

# Test TTS
curl -X POST "http://localhost:8000/api/speak?text=Hello&lang=en" --output test.mp3
```

## 🔄 Complete Reset

```bash
# Kill all processes
pkill -f 'uvicorn|react-scripts'

# Restart everything
./start_chatbot.sh
```

## ✅ System Status Check

Run this to verify everything is working:
```bash
echo "Backend Health:" && curl -s http://localhost:8000/health
echo -e "\nFrontend Status:" && curl -s -I http://localhost:3000 | head -1
echo -e "\nChat Test:" && curl -s -X POST "http://localhost:8000/api/chat" -H "Content-Type: application/json" -d '{"message": "test", "context": [], "session_id": null, "use_voice": false}' | jq -r '.response' | head -1
```
