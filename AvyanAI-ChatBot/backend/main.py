import os
import io
import uuid
import tempfile
import logging
import async<PERSON>
from datetime import datetime
from typing import List, Dict, Optional, Tu<PERSON>
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import httpx
from dotenv import load_dotenv

# Audio processing imports
try:
    import speech_recognition as sr
    from gtts import gTTS
    AUDIO_AVAILABLE = True
except ImportError:
    AUDIO_AVAILABLE = False
    print("Audio processing not available")

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
HUGGINGFACE_TOKEN = os.getenv("HF_TOKEN")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
SARVAM_MODEL_ID = "microsoft/DialoGPT-medium"
FALLBACK_MODEL_ID = "facebook/blenderbot-400M-distill"

# Supported languages - Hindi and English bilingual support
SUPPORTED_LANGUAGES = {
    'en': 'English',
    'hi': 'हिंदी (Hindi)'
}

# Initialize PDF Pipeline
pdf_pipeline = None

async def init_pdf_pipeline():
    """Initialize PDF pipeline on startup"""
    global pdf_pipeline
    try:
        from pdf_pipeline import create_pdf_pipeline
        pdf_pipeline = await create_pdf_pipeline()
        # Start file monitoring
        pdf_pipeline.start_monitoring()
        logger.info("PDF pipeline initialized and monitoring started")
    except Exception as e:
        logger.error(f"Failed to initialize PDF pipeline: {e}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    # Startup
    await init_pdf_pipeline()
    yield
    # Shutdown
    if pdf_pipeline:
        pdf_pipeline.stop_monitoring()

# Initialize FastAPI with lifespan
app = FastAPI(title="Avyan AI Chatbot API", version="1.0.0", lifespan=lifespan)

# CORS configuration with support for both HTTP and HTTPS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://localhost:3000",
        "https://127.0.0.1:3000",
        "*"  # Allow all for development - restrict in production
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize RAG system
rag_system = None
try:
    from multimodal_rag import MultimodalRAG
    rag_system = MultimodalRAG("../data")
    logger.info("RAG system initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize RAG system: {e}")



# Include pipeline API routes
try:
    from pipeline_api import router as pipeline_router
    app.include_router(pipeline_router)
    logger.info("Pipeline API routes added successfully")
except Exception as e:
    logger.error(f"Failed to add pipeline API routes: {e}")

# Session management
sessions = {}

# Models with multilingual support
class ChatRequest(BaseModel):
    message: str
    context: List[Dict] = []
    session_id: Optional[str] = None
    use_voice: bool = False
    language: str = 'en'  # Default to English

class ChatResponse(BaseModel):
    response: str
    session_id: str
    timestamp: datetime
    sources: List[Dict] = []
    suggested_videos: List[Dict] = []

# Utility functions
def get_or_create_session(session_id: Optional[str] = None) -> str:
    """Get existing session or create new one"""
    if not session_id:
        session_id = str(uuid.uuid4())
    
    if session_id not in sessions:
        sessions[session_id] = {
            "created_at": datetime.now(),
            "messages": [],
            "context": [],
            "language": "en"
        }
    return session_id

async def translate_text(text: str, target_lang: str, source_lang: str = 'auto') -> str:
    """Translate text using Hugging Face translation models"""
    if target_lang == 'en' or target_lang == source_lang:
        return text

    try:
        # Simple translation using Hugging Face translation models
        if source_lang == 'auto':
            # Detect if text contains Hindi characters
            if any('\u0900' <= char <= '\u097F' for char in text):
                source_lang = 'hi'
            else:
                source_lang = 'en'

        # Skip translation if already in target language
        if source_lang == target_lang:
            return text

        # Determine translation model
        if source_lang == 'hi' and target_lang == 'en':
            model_name = "Helsinki-NLP/opus-mt-hi-en"
        elif source_lang == 'en' and target_lang == 'hi':
            model_name = "Helsinki-NLP/opus-mt-en-hi"
        else:
            # Fallback: return original text if unsupported language pair
            return text

        headers = {"Authorization": f"Bearer {HUGGINGFACE_TOKEN}"}
        payload = {"inputs": text}

        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"https://api-inference.huggingface.co/models/{model_name}",
                headers=headers,
                json=payload
            )

            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    translated = result[0].get("translation_text", text)
                    return translated

        return text  # Return original if translation fails

    except Exception as e:
        logger.error(f"Translation error: {e}")
        return text  # Return original text on error

async def query_ai_model(message: str, context: List[dict] = None, language: str = 'en') -> Tuple[str, List[dict]]:
    """Query AI model with RAG context"""
    try:
        # Get RAG context first
        rag_context = ""
        sources = []

        # Try PDF pipeline first for more recent/specific documents
        if pdf_pipeline:
            try:
                pdf_results = await pdf_pipeline.search(message, top_k=3)
                if pdf_results:
                    pdf_context = "\n\n".join([result['content'] for result in pdf_results])
                    rag_context += f"Recent PDF Documents:\n{pdf_context}\n\n"
                    sources.extend([{
                        'content': result['content'],
                        'metadata': result['metadata'],
                        'score': result['score'],
                        'collection': 'pdf_pipeline'
                    } for result in pdf_results])
                    logger.info(f"Retrieved {len(pdf_results)} results from PDF pipeline")
            except Exception as e:
                logger.error(f"Error retrieving PDF pipeline context: {e}")

        # Get additional context from multimodal RAG system
        if rag_system:
            try:
                additional_context, additional_sources = await rag_system.get_context_for_query(message)
                if additional_context:
                    rag_context += f"Additional Context:\n{additional_context}"
                    sources.extend(additional_sources)
                logger.info(f"Retrieved additional RAG context with {len(additional_sources)} sources")
            except Exception as e:
                logger.error(f"Error retrieving RAG context: {e}")

        # Try intelligent response first for better quality and Hindi support
        if rag_context and sources:
            intelligent_response = generate_intelligent_response(message, rag_context, language)
            if intelligent_response and len(intelligent_response) > 50:  # If we get a good response
                return intelligent_response, sources

        # Build comprehensive prompt
        if rag_context:
            prompt = f"""You are Avyan AI's intelligent assistant. You help users understand Avyan AI's services, technologies, and capabilities.

Use the following context to provide comprehensive, helpful, and professional responses. Focus on being informative and specific about Avyan AI's offerings.

Context: {rag_context}

Instructions:
- Provide detailed, structured responses
- Focus on practical benefits and real-world applications
- Be professional but conversational
- Don't mention sources or internal references
- If asked about services, list them clearly with brief descriptions

User Question: {message}

Assistant Response:"""
        else:
            prompt = f"""You are Avyan AI's intelligent assistant. Answer questions about Avyan AI professionally and helpfully.

Avyan AI is a cutting-edge artificial intelligence company that focuses on practical AI solutions. Our mission is "Build Smarter. Move Faster. - Practical AI That Works, Not Just Impresses."

User Question: {message}

Assistant Response:"""

        # Try Sarvam model with proper authentication
        headers = {
            "Authorization": f"Bearer {HUGGINGFACE_TOKEN}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": 400,  # Increased for more comprehensive responses
                "temperature": 0.3,     # Lower temperature for more focused responses
                "do_sample": True,
                "top_p": 0.85,
                "repetition_penalty": 1.1,
                "return_full_text": False
            },
            "options": {
                "wait_for_model": True,
                "use_cache": False
            }
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"https://api-inference.huggingface.co/models/{SARVAM_MODEL_ID}",
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    generated_text = result[0].get("generated_text", "").strip()
                    if generated_text:
                        return generated_text, sources
                
            # If model fails, use intelligent RAG-based response
            if rag_context and sources:
                return generate_intelligent_response(message, rag_context, language), sources
            else:
                return "I'm here to help with information about Avyan AI! Could you please ask me something specific?", sources
                
    except Exception as e:
        logger.error(f"Error querying AI model: {e}")
        # Final fallback to RAG if available
        if rag_system:
            try:
                rag_context, sources = await rag_system.get_context_for_query(message)
                if rag_context:
                    return generate_intelligent_response(message, rag_context, language), sources
            except:
                pass
        return "I'm sorry, I encountered an error. Please try again.", []

def extract_meaningful_content(rag_context: str) -> Dict[str, List[str]]:
    """Extract and categorize meaningful content from RAG context"""
    lines = rag_context.split('\n')

    services = []
    technologies = []
    company_info = []
    contact_info = []
    features = []

    # Define comprehensive service patterns
    service_patterns = [
        'training & enablement', 'corporate training', 'team upskilling', 'generative ai',
        'rag chatbot', 'intelligent chatbot', 'customer support', 'ai agents', 'sdlc framework',
        'energy consumption', 'predictive forecasting', 'telecom tower', 'smart city',
        'healthcare', 'gen ai vision', 'strategy execution', 'llm fine-tuning',
        'data solutions', 'cloud solutions', 'ai solutions'
    ]

    for line in lines:
        line = line.strip()
        if not line or line.startswith('---') or line.startswith('Page') or len(line) < 10:
            continue

        line_lower = line.lower()

        # Extract services with better pattern matching
        if any(pattern in line_lower for pattern in service_patterns):
            # Clean up the line
            clean_line = line.replace('•', '').replace('-', '').strip()
            if clean_line and len(clean_line) > 15 and clean_line not in services:
                services.append(clean_line)

        # Extract specific service descriptions
        elif any(keyword in line_lower for keyword in ['customized sessions', 'hands-on workshops', 'continuous learning']):
            clean_line = line.replace('•', '').replace('-', '').strip()
            if clean_line and len(clean_line) > 15 and clean_line not in services:
                services.append(clean_line)

        # Extract technologies
        elif any(keyword in line_lower for keyword in ['machine learning', 'natural language', 'deep learning', 'neural network']):
            clean_line = line.replace('•', '').replace('-', '').strip()
            if clean_line and len(clean_line) > 15 and clean_line not in technologies:
                technologies.append(clean_line)

        # Extract company information
        elif any(keyword in line_lower for keyword in ['build smarter', 'move faster', 'practical ai', '20+ years']):
            clean_line = line.replace('•', '').replace('-', '').strip()
            if clean_line and len(clean_line) > 15 and clean_line not in company_info:
                company_info.append(clean_line)

        # Extract features and capabilities
        elif any(keyword in line_lower for keyword in ['efficiency', 'growth', 'innovation', 'transformation']):
            clean_line = line.replace('•', '').replace('-', '').strip()
            if clean_line and len(clean_line) > 15 and clean_line not in features:
                features.append(clean_line)

    # Add predefined services if none found
    if not services:
        services = [
            "AI-powered RAG Chatbots for Customer Support",
            "Generative AI Training and Team Upskilling",
            "AI Agents for Software Development Lifecycle (SDLC)",
            "Predictive Analytics for Energy and Telecom",
            "Smart City AI Solutions",
            "Healthcare AI Applications",
            "Corporate AI Strategy and Implementation"
        ]

    return {
        'services': services[:7],  # Increased limit
        'technologies': technologies[:4],
        'company_info': company_info[:3],
        'contact_info': contact_info[:2],
        'features': features[:4]
    }

def generate_intelligent_response(user_message: str, rag_context: str, language: str = 'en') -> str:
    """Generate intelligent responses based on RAG context with language support"""
    user_message_lower = user_message.lower()

    # Extract meaningful content
    content = extract_meaningful_content(rag_context)

    # Check if the message is in Hindi
    is_hindi = any('\u0900' <= char <= '\u097F' for char in user_message) or language == 'hi'

    # Hindi contact phrases
    hindi_contact_phrases = ['संपर्क', 'कैसे संपर्क', 'कैसे मिलें', 'संपर्क करें', 'कैसे बात करें']

    # Question type detection and response generation
    # Check for contact questions first
    if (any(phrase in user_message_lower for phrase in ['contact', 'reach', 'get in touch', 'how can i contact', 'contact avyan', 'how to contact', 'reach out']) or
        any(phrase in user_message for phrase in hindi_contact_phrases)):

        if is_hindi:
            response = "आप निम्नलिखित माध्यमों से Avyan AI से संपर्क कर सकते हैं:\n\n"
            response += "• **वेबसाइट:** https://avyan.ai/\n"
            response += "• **ईमेल:** <EMAIL>\n\n"
            response += "हम आपको व्यावहारिक AI समाधान लागू करने में मदद करने के लिए यहाँ हैं जो वास्तविक व्यावसायिक मूल्य प्रदान करते हैं। परामर्श, साझेदारी, या हमारी सेवाओं के बारे में किसी भी प्रश्न के लिए बेझिझक संपर्क करें।"
        else:
            response = "You can reach Avyan AI through the following channels:\n\n"
            response += "• **Website:** https://avyan.ai/\n"
            response += "• **Email:** <EMAIL>\n\n"
            response += "We're here to help you implement practical AI solutions that drive real business value. Feel free to reach out for consultations, partnerships, or any questions about our services."
        return response

    elif (any(phrase in user_message_lower for phrase in ['service', 'services', 'offer', 'provide', 'what does avyan do', 'what do you do', 'what can you help with']) or
          any(phrase in user_message for phrase in ['सेवा', 'सेवाएं', 'क्या करता है', 'क्या सेवाएं', 'कैसे मदद'])):

        if is_hindi:
            response = "Avyan AI विभिन्न उद्योगों में व्यावहारिक कृत्रिम बुद्धिमत्ता समाधान प्रदान करने में विशेषज्ञ है। हमारी मुख्य सेवाओं में शामिल हैं:\n\n"

            # Define core services in Hindi
            core_services_hindi = [
                "**AI-संचालित RAG चैटबॉट्स** - बेहतर उपयोगकर्ता अनुभव के लिए रिट्रीवल-ऑगमेंटेड जेनरेशन के साथ बुद्धिमान ग्राहक सहायता सिस्टम",
                "**जेनेरेटिव AI प्रशिक्षण और सक्षमता** - अत्याधुनिक AI तकनीकों में व्यापक कॉर्पोरेट प्रशिक्षण और टीम अपस्किलिंग कार्यक्रम",
                "**SDLC के लिए AI एजेंट्स** - प्रक्रियाओं को सुव्यवस्थित करने के लिए बुद्धिमान AI एजेंट्स का उपयोग करके स्वचालित सॉफ्टवेयर विकास जीवनचक्र प्रबंधन",
                "**प्रेडिक्टिव एनालिटिक्स समाधान** - ऊर्जा खपत, टेलीकॉम अनुकूलन, और व्यावसायिक बुद्धिमत्ता के लिए उन्नत पूर्वानुमान सिस्टम",
                "**स्मार्ट सिटी AI समाधान** - बेहतर शहर प्रबंधन के लिए बुद्धिमान AI अनुप्रयोगों का उपयोग करके शहरी परिवर्तन पहल",
                "**हेल्थकेयर AI एप्लिकेशन** - रोगी देखभाल और स्वास्थ्य सेवा परिचालन दक्षता बढ़ाने के लिए डिज़ाइन किए गए मेडिकल AI समाधान",
                "**AI रणनीति और कार्यान्वयन** - आपकी व्यावसायिक आवश्यकताओं के अनुकूल एंड-टू-एंड AI विज़न विकास और निष्पादन फ्रेमवर्क"
            ]

            # Add services in Hindi
            for i, service in enumerate(core_services_hindi, 1):
                response += f"{i}. {service}\n\n"

            response += "**Avyan AI को क्यों चुनें?**\n"
            response += "• 20+ वर्षों का संयुक्त डेटा और क्लाउड अनुभव\n"
            response += "• व्यावहारिक AI समाधानों पर ध्यान जो मापने योग्य व्यावसायिक मूल्य प्रदान करते हैं\n"
            response += "• रणनीति से तैनाती तक एंड-टू-एंड कार्यान्वयन\n"
            response += "• आपकी अनूठी आवश्यकताओं के अनुकूल उद्योग-विशिष्ट समाधान\n\n"

            response += "**शुरुआत करें:** https://avyan.ai/ पर जाएं या <EMAIL> पर हमसे संपर्क करें"
        else:
            response = "Avyan AI specializes in delivering practical artificial intelligence solutions across various industries. Our key services include:\n\n"

            # Define core services with clean descriptions
            core_services = [
                "**AI-Powered RAG Chatbots** - Intelligent customer support systems with retrieval-augmented generation for enhanced user experiences",
                "**Generative AI Training & Enablement** - Comprehensive corporate training and team upskilling programs in cutting-edge AI technologies",
                "**AI Agents for SDLC** - Automated software development lifecycle management using intelligent AI agents to streamline processes",
                "**Predictive Analytics Solutions** - Advanced forecasting systems for energy consumption, telecom optimization, and business intelligence",
                "**Smart City AI Solutions** - Urban transformation initiatives using intelligent AI applications for improved city management",
                "**Healthcare AI Applications** - Medical AI solutions designed to enhance patient care and healthcare operational efficiency",
                "**AI Strategy & Implementation** - End-to-end AI vision development and execution frameworks tailored to your business needs"
            ]

            # Always use predefined core services for consistency
            for i, service in enumerate(core_services, 1):
                response += f"{i}. {service}\n\n"

            response += "**Why Choose Avyan AI?**\n"
            response += "• 20+ years of combined data and cloud experience\n"
            response += "• Focus on practical AI solutions that deliver measurable business value\n"
            response += "• End-to-end implementation from strategy to deployment\n"
            response += "• Industry-specific solutions tailored to your unique requirements\n\n"

            response += "**Get Started:** Visit https://avyan.ai/ or contact <NAME_EMAIL>"

        return response

    elif (any(word in user_message_lower for word in ['what is', 'what are', 'tell me about', 'explain']) or
          any(phrase in user_message for phrase in ['क्या है', 'कैसा है', 'बताइए', 'समझाइए'])):
        if ('avyan ai' in user_message_lower or 'avyan' in user_message_lower or
            'avyan ai' in user_message or 'avyan' in user_message):

            if is_hindi:
                response = "Avyan AI एक अत्याधुनिक कृत्रिम बुद्धिमत्ता कंपनी है जो वास्तविक दुनिया के परिदृश्यों में काम करने वाले व्यावहारिक AI समाधान प्रदान करने पर केंद्रित है।\n\n"

                response += "**हमारे बारे में:**\n"
                response += "• 20+ वर्षों का डेटा और क्लाउड अनुभव\n"
                response += "• व्यावहारिक AI समाधानों पर विशेषज्ञता\n"
                response += "• उद्योग-विशिष्ट समाधान प्रदान करते हैं\n\n"

                response += "**हमारा मिशन:** Build Smarter. Move Faster. - व्यावहारिक AI जो काम करती है, सिर्फ प्रभावित नहीं करती।\n\n"
                response += "अधिक जानकारी के लिए https://avyan.ai/ पर जाएं।"
            else:
                response = "Avyan AI is a cutting-edge artificial intelligence company focused on delivering practical AI solutions that work in real-world scenarios.\n\n"

                if content['company_info']:
                    response += "About us:\n"
                    for info in content['company_info'][:2]:
                        clean_info = info.replace('•', '').strip()
                        if clean_info and len(clean_info) > 10:
                            response += f"• {clean_info}\n"

                response += "\nOur mission: Build Smarter. Move Faster. - Practical AI That Works, Not Just Impresses."
            return response.strip()

    elif any(word in user_message_lower for word in ['how', 'how to', 'process', 'approach']):
        response = "At Avyan AI, we approach AI implementation with a focus on practical, results-driven solutions:\n\n"

        if content['technologies']:
            response += "Our methodology includes:\n"
            for tech in content['technologies'][:3]:
                clean_tech = tech.replace('•', '').strip()
                if clean_tech and len(clean_tech) > 10:
                    response += f"• {clean_tech}\n"

        return response.strip()

    elif any(word in user_message_lower for word in ['technology', 'tech', 'ai', 'artificial intelligence']):
        response = "Avyan AI leverages cutting-edge artificial intelligence technologies:\n\n"

        if content['technologies']:
            for tech in content['technologies']:
                clean_tech = tech.replace('•', '').strip()
                if clean_tech and len(clean_tech) > 10:
                    response += f"• {clean_tech}\n"

        return response.strip()



    # Default comprehensive response
    if any(content.values()):
        response = "Here's what I can tell you about Avyan AI:\n\n"

        if content['company_info']:
            response += f"{content['company_info'][0]}\n\n"

        if content['services']:
            response += "Our key services include:\n"
            for service in content['services'][:3]:
                clean_service = service.replace('•', '').strip()
                if clean_service and len(clean_service) > 15:
                    response += f"• {clean_service}\n"

        response += "\nFor more detailed information, feel free to ask about our specific services or visit https://avyan.ai/"
        return response.strip()
    else:
        return "I'd be happy to help you learn more about Avyan AI! You can ask me about our services, technologies, or how we can help with your AI needs. Visit https://avyan.ai/ for more information."

# Audio processing functions
async def transcribe_audio(audio_file: UploadFile) -> str:
    """Transcribe audio using SpeechRecognition library"""
    if not AUDIO_AVAILABLE:
        return "Audio processing not available"
    
    try:
        await audio_file.seek(0)
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as temp_file:
            content = await audio_file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            recognizer = sr.Recognizer()
            with sr.AudioFile(temp_file_path) as source:
                recognizer.adjust_for_ambient_noise(source, duration=0.5)
                audio_data = recognizer.record(source)
                text = recognizer.recognize_google(audio_data)
            
            os.unlink(temp_file_path)
            return text.strip()
            
        except sr.UnknownValueError:
            os.unlink(temp_file_path)
            return "Sorry, I couldn't understand the audio. Please try speaking more clearly."
            
        except sr.RequestError as e:
            os.unlink(temp_file_path)
            return "Speech recognition service is temporarily unavailable."
        
    except Exception as e:
        logger.error(f"Error in transcription: {e}")
        return "Failed to process audio. Please try again."

async def generate_speech(text: str, lang: str = "en") -> bytes:
    """Generate speech from text using gTTS"""
    if not AUDIO_AVAILABLE:
        raise HTTPException(status_code=500, detail="Audio processing not available")
    
    try:
        tts = gTTS(text=text, lang=lang, slow=False)
        audio_buffer = io.BytesIO()
        tts.write_to_fp(audio_buffer)
        audio_buffer.seek(0)
        return audio_buffer.getvalue()
    except Exception as e:
        logger.error(f"Error generating speech: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate speech")

# API Endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Avyan AI Chatbot API", "status": "running"}

@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint with multilingual support"""
    try:
        session_id = get_or_create_session(request.session_id)

        # Update session language preference
        if session_id in sessions:
            sessions[session_id]["language"] = request.language

        # Translate input message to English if needed for processing
        original_message = request.message
        processing_message = original_message
        if request.language != 'en':
            processing_message = await translate_text(original_message, 'en', request.language)

        # Get AI response with language context
        response_text, sources = await query_ai_model(processing_message, request.context, request.language)

        # Translate response back to user's language if needed
        if request.language != 'en':
            response_text = await translate_text(response_text, request.language, 'en')

        # Log sources for internal tracking but don't send to frontend
        if sources:
            logger.info(f"Response generated using {len(sources)} sources for session {session_id}")

        response = ChatResponse(
            response=response_text,
            session_id=session_id,
            timestamp=datetime.now(),
            sources=[],  # Don't send sources to frontend
            suggested_videos=[]
        )

        return response

    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/api/transcribe")
async def transcribe_endpoint(audio: UploadFile = File(...), session_id: Optional[str] = Form(None)):
    """Transcribe uploaded audio file"""
    try:
        if not audio.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="File must be an audio file")

        transcription = await transcribe_audio(audio)

        return {
            "transcription": transcription,
            "session_id": get_or_create_session(session_id)
        }

    except Exception as e:
        logger.error(f"Error in transcribe endpoint: {e}")
        raise HTTPException(status_code=500, detail="Failed to transcribe audio")

@app.post("/api/speak")
async def text_to_speech(text: str, lang: str = "en"):
    """Convert text to speech"""
    try:
        audio_data = await generate_speech(text, lang)

        return StreamingResponse(
            io.BytesIO(audio_data),
            media_type="audio/mpeg",
            headers={"Content-Disposition": "attachment; filename=speech.mp3"}
        )

    except Exception as e:
        logger.error(f"Error in TTS endpoint: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate speech")

@app.post("/api/upload-document")
async def upload_document(file: UploadFile = File(...)):
    """Upload and process new documents for RAG system"""
    try:
        if not rag_system:
            raise HTTPException(status_code=500, detail="RAG system not available")

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file.filename.split('.')[-1]}") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Process the document with RAG system
            await rag_system.add_document(temp_file_path, file.filename)

            # Clean up temp file
            os.unlink(temp_file_path)

            return {
                "message": f"Document '{file.filename}' uploaded and processed successfully",
                "filename": file.filename,
                "timestamp": datetime.now()
            }

        except Exception as e:
            # Clean up temp file on error
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
            raise e

    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail="Failed to upload document")

@app.get("/api/languages")
async def get_supported_languages():
    """Get list of supported languages"""
    return {
        "languages": SUPPORTED_LANGUAGES,
        "default": "en"
    }

class TranslationRequest(BaseModel):
    text: str
    target_lang: str
    source_lang: str = 'auto'

@app.post("/api/translate")
async def translate_endpoint(request: TranslationRequest):
    """Translate text to target language"""
    try:
        translated_text = await translate_text(request.text, request.target_lang, request.source_lang)
        return {
            "original_text": request.text,
            "translated_text": translated_text,
            "source_language": request.source_lang,
            "target_language": request.target_lang
        }
    except Exception as e:
        logger.error(f"Translation error: {e}")
        raise HTTPException(status_code=500, detail="Translation failed")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "rag_system": rag_system is not None,
        "pdf_pipeline": pdf_pipeline is not None,
        "audio_available": AUDIO_AVAILABLE,
        "huggingface_token": HUGGINGFACE_TOKEN is not None,
        "gemini_api_key": GEMINI_API_KEY is not None,
        "supported_languages": len(SUPPORTED_LANGUAGES),
        "features": {
            "multilingual": True,
            "document_upload": True,
            "pdf_pipeline": pdf_pipeline is not None,
            "voice_processing": AUDIO_AVAILABLE,
            "rag_system": rag_system is not None,
            "translation": True
        }
    }

if __name__ == "__main__":
    import uvicorn
    # Check if SSL certificates exist for HTTPS
    ssl_keyfile = "../frontend/certs/localhost.key"
    ssl_certfile = "../frontend/certs/localhost.crt"

    try:
        with open(ssl_keyfile), open(ssl_certfile):
            # Run with HTTPS if certificates exist
            logger.info("SSL certificates found, starting server with HTTPS")
            uvicorn.run(
                app,
                host="0.0.0.0",
                port=8000,
                ssl_keyfile=ssl_keyfile,
                ssl_certfile=ssl_certfile
            )
    except FileNotFoundError:
        # Fall back to HTTP if no certificates
        logger.info("No SSL certificates found, starting server with HTTP")
        uvicorn.run(app, host="0.0.0.0", port=8000)
