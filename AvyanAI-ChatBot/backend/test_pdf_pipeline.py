#!/usr/bin/env python3
"""
Comprehensive test suite for the PDF processing pipeline
"""

import asyncio
import logging
import sys
import json
from pathlib import Path
from typing import Dict, Any

# Add the backend directory to the Python path
sys.path.append(str(Path(__file__).parent))

from pdf_pipeline import create_pdf_pipeline, PDFProcessor, IntelligentChunker

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PipelineTestSuite:
    """Test suite for PDF processing pipeline"""
    
    def __init__(self):
        self.pipeline = None
        self.test_results = {}
    
    async def setup(self):
        """Setup test environment"""
        try:
            logger.info("Setting up test environment...")
            self.pipeline = await create_pdf_pipeline()
            logger.info("Pipeline initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to setup test environment: {e}")
            return False
    
    async def test_pdf_detection(self) -> bool:
        """Test PDF file detection"""
        try:
            logger.info("Testing PDF file detection...")
            
            services_folder = Path("data/documents/services")
            if not services_folder.exists():
                logger.error(f"Services folder not found: {services_folder}")
                return False
            
            pdf_files = list(services_folder.glob("*.pdf"))
            logger.info(f"Found {len(pdf_files)} PDF files")
            
            if len(pdf_files) == 0:
                logger.warning("No PDF files found for testing")
                return False
            
            # Log found files
            for pdf_file in pdf_files:
                logger.info(f"  - {pdf_file.name} ({pdf_file.stat().st_size} bytes)")
            
            self.test_results["pdf_detection"] = {
                "status": "passed",
                "files_found": len(pdf_files),
                "files": [str(f) for f in pdf_files]
            }
            return True
            
        except Exception as e:
            logger.error(f"PDF detection test failed: {e}")
            self.test_results["pdf_detection"] = {
                "status": "failed",
                "error": str(e)
            }
            return False
    
    async def test_text_extraction(self) -> bool:
        """Test text extraction from PDFs"""
        try:
            logger.info("Testing text extraction...")
            
            processor = PDFProcessor()
            services_folder = Path("data/documents/services")
            pdf_files = list(services_folder.glob("*.pdf"))
            
            if not pdf_files:
                logger.warning("No PDF files available for text extraction test")
                return False
            
            # Test with first PDF file
            test_file = pdf_files[0]
            logger.info(f"Testing text extraction with: {test_file.name}")
            
            text_content, metadata = await processor.process_pdf(test_file)
            
            if not text_content.strip():
                logger.error("No text extracted from PDF")
                return False
            
            logger.info(f"Successfully extracted {len(text_content)} characters")
            logger.info(f"Extraction method: {metadata.extraction_method}")
            logger.info(f"Total pages: {metadata.total_pages}")
            logger.info(f"Has tables: {metadata.has_tables}")
            logger.info(f"Has images: {metadata.has_images}")
            
            self.test_results["text_extraction"] = {
                "status": "passed",
                "file_tested": str(test_file),
                "text_length": len(text_content),
                "metadata": {
                    "total_pages": metadata.total_pages,
                    "extraction_method": metadata.extraction_method,
                    "has_tables": metadata.has_tables,
                    "has_images": metadata.has_images
                }
            }
            return True
            
        except Exception as e:
            logger.error(f"Text extraction test failed: {e}")
            self.test_results["text_extraction"] = {
                "status": "failed",
                "error": str(e)
            }
            return False
    
    async def test_chunking(self) -> bool:
        """Test text chunking functionality"""
        try:
            logger.info("Testing text chunking...")
            
            # Create sample text for testing
            sample_text = """
            --- Page 1 ---
            Avyan AI is a leading artificial intelligence company that specializes in developing innovative AI solutions for businesses. Our services include machine learning model development, natural language processing, computer vision, and data analytics.
            
            --- Page 2 ---
            We offer comprehensive AI consulting services to help organizations implement AI-driven solutions. Our team of experts works closely with clients to understand their specific needs and develop customized AI strategies.
            
            --- Page 3 ---
            Our portfolio includes chatbot development, predictive analytics, automated document processing, and intelligent recommendation systems. We have successfully delivered projects across various industries including healthcare, finance, and retail.
            """
            
            # Create mock metadata
            from pdf_pipeline import ProcessingMetadata
            from datetime import datetime
            
            mock_metadata = ProcessingMetadata(
                file_path="test_file.pdf",
                file_hash="test_hash",
                processed_at=datetime.now(),
                total_pages=3,
                total_chunks=0,
                extraction_method="test",
                has_tables=False,
                has_images=False,
                file_size=1000
            )
            
            chunker = IntelligentChunker(chunk_size=200, chunk_overlap=50)
            documents = chunker.create_chunks(sample_text, mock_metadata)
            
            if not documents:
                logger.error("No chunks created")
                return False
            
            logger.info(f"Successfully created {len(documents)} chunks")
            
            # Verify chunk metadata
            for i, doc in enumerate(documents[:3]):  # Check first 3 chunks
                logger.info(f"Chunk {i+1}: {len(doc.page_content)} characters")
                logger.info(f"  Metadata keys: {list(doc.metadata.keys())}")
            
            self.test_results["chunking"] = {
                "status": "passed",
                "chunks_created": len(documents),
                "sample_chunk_length": len(documents[0].page_content) if documents else 0
            }
            return True
            
        except Exception as e:
            logger.error(f"Chunking test failed: {e}")
            self.test_results["chunking"] = {
                "status": "failed",
                "error": str(e)
            }
            return False
    
    async def test_pipeline_processing(self) -> bool:
        """Test end-to-end pipeline processing"""
        try:
            logger.info("Testing end-to-end pipeline processing...")
            
            services_folder = Path("data/documents/services")
            pdf_files = list(services_folder.glob("*.pdf"))
            
            if not pdf_files:
                logger.warning("No PDF files available for pipeline test")
                return False
            
            # Test with first PDF file
            test_file = pdf_files[0]
            logger.info(f"Testing pipeline with: {test_file.name}")
            
            success = await self.pipeline.process_single_file(test_file)
            
            if not success:
                logger.error("Pipeline processing failed")
                return False
            
            logger.info("Pipeline processing completed successfully")
            
            # Get processing stats
            stats = self.pipeline.get_processing_stats()
            logger.info(f"Processing stats: {stats}")
            
            self.test_results["pipeline_processing"] = {
                "status": "passed",
                "file_tested": str(test_file),
                "stats": stats
            }
            return True
            
        except Exception as e:
            logger.error(f"Pipeline processing test failed: {e}")
            self.test_results["pipeline_processing"] = {
                "status": "failed",
                "error": str(e)
            }
            return False
    
    async def test_search_functionality(self) -> bool:
        """Test search functionality"""
        try:
            logger.info("Testing search functionality...")
            
            # Test queries
            test_queries = [
                "Avyan AI",
                "artificial intelligence",
                "machine learning",
                "services"
            ]
            
            search_results = {}
            
            for query in test_queries:
                logger.info(f"Testing search for: '{query}'")
                results = await self.pipeline.search(query, top_k=3)
                
                search_results[query] = {
                    "results_count": len(results),
                    "top_score": results[0]["score"] if results else 0
                }
                
                if results:
                    logger.info(f"  Found {len(results)} results, top score: {results[0]['score']:.3f}")
                else:
                    logger.warning(f"  No results found for '{query}'")
            
            self.test_results["search_functionality"] = {
                "status": "passed",
                "search_results": search_results
            }
            return True
            
        except Exception as e:
            logger.error(f"Search functionality test failed: {e}")
            self.test_results["search_functionality"] = {
                "status": "failed",
                "error": str(e)
            }
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests and return results"""
        logger.info("Starting comprehensive pipeline test suite...")
        
        # Setup
        if not await self.setup():
            return {"status": "failed", "error": "Setup failed"}
        
        # Run tests
        tests = [
            ("PDF Detection", self.test_pdf_detection),
            ("Text Extraction", self.test_text_extraction),
            ("Chunking", self.test_chunking),
            ("Pipeline Processing", self.test_pipeline_processing),
            ("Search Functionality", self.test_search_functionality)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                if result:
                    logger.info(f"✅ {test_name} PASSED")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name} FAILED")
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED with exception: {e}")
        
        # Summary
        logger.info(f"\n{'='*50}")
        logger.info(f"TEST SUMMARY: {passed}/{total} tests passed")
        logger.info(f"{'='*50}")
        
        return {
            "status": "completed",
            "passed": passed,
            "total": total,
            "success_rate": passed / total if total > 0 else 0,
            "test_results": self.test_results
        }

async def main():
    """Main function"""
    test_suite = PipelineTestSuite()
    results = await test_suite.run_all_tests()
    
    # Save results to file
    results_file = Path("test_results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    logger.info(f"Test results saved to: {results_file}")
    
    # Exit with appropriate code
    if results["status"] == "completed" and results["success_rate"] == 1.0:
        logger.info("🎉 All tests passed!")
        sys.exit(0)
    else:
        logger.error("❌ Some tests failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
